/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Enhanced Animation Overlay Styles */
.animation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-dark);
  background-image:
    radial-gradient(circle at 20% 30%, rgba(0, 166, 192, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(0, 166, 192, 0.03) 0%, transparent 50%),
    linear-gradient(135deg, #0a0f14 0%, #121820 100%);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 1; /* Changed from 0 to 1 to make it visible by default */
  pointer-events: all; /* Enable pointer events */
  transition: opacity 0.3s ease;
  overflow: hidden;
}

.animation-overlay.active {
  opacity: 1;
  pointer-events: all;
}

.animation-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  position: relative;
  width: 100%;
  height: 100%;
}

.animation-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.particle {
  position: absolute;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0;
  box-shadow: 0 0 10px 2px rgba(0, 166, 192, 0.3);
}

.particle-1 {
  width: 15px;
  height: 15px;
  top: 20%;
  left: 30%;
}

.particle-2 {
  width: 10px;
  height: 10px;
  top: 60%;
  left: 20%;
}

.particle-3 {
  width: 20px;
  height: 20px;
  top: 40%;
  left: 70%;
}

.particle-4 {
  width: 12px;
  height: 12px;
  top: 70%;
  left: 60%;
}

.particle-5 {
  width: 8px;
  height: 8px;
  top: 30%;
  left: 80%;
}

.particle-6 {
  width: 6px;
  height: 6px;
  top: 25%;
  left: 45%;
  background: var(--primary-light);
}

.particle-7 {
  width: 18px;
  height: 18px;
  top: 65%;
  left: 75%;
  background: var(--primary-light);
}

.particle-8 {
  width: 14px;
  height: 14px;
  top: 50%;
  left: 15%;
  background: var(--primary-light);
}

.glow-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px; /* Increased from 250px */
  height: 300px; /* Increased from 250px */
  background: radial-gradient(circle, rgba(0, 216, 224, 0.4) 0%, transparent 70%); /* Brighter color */
  border-radius: 50%;
  opacity: 0;
  filter: blur(15px); /* Added blur for softer glow */
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 180px; /* Increased from 150px */
  height: 180px; /* Increased from 150px */
  border: 3px solid rgba(0, 216, 224, 0.4); /* Thicker border and brighter color */
  border-radius: 50%;
  opacity: 0;
  box-shadow: 0 0 20px rgba(0, 216, 224, 0.3); /* Added glow */
}

.logo-animation {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.animated-logo {
  width: 100px; /* Increased from 80px */
  height: 100px; /* Increased from 80px */
  border-radius: 50%;
  opacity: 0;
  transform: scale(0.5);
  filter: drop-shadow(0 0 20px rgba(0, 216, 224, 0.8)); /* Enhanced glow */
  animation: none; /* Will be controlled by GSAP */
  position: relative;
}

.animated-logo::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  border: 2px solid rgba(0, 216, 224, 0.4);
  opacity: 0;
  z-index: -1;
}

/* Brand text animation */
.brand-animation {
  position: relative;
  z-index: 2;
  margin-top: 10px;
  margin-bottom: 5px;
}

.brand-text {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32px; /* Increased from 24px */
  font-weight: 700;
  color: var(--text-color);
  letter-spacing: 2px; /* Increased from 1px */
  background: linear-gradient(to right, var(--text-color), var(--primary-light), var(--text-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 0 12px rgba(0, 216, 224, 0.6));
}

.letter {
  opacity: 0;
  transform: translateY(10px);
  display: inline-block;
  position: relative;
}

.letter::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--primary-color);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
  opacity: 0;
}

.letter.space {
  width: 12px; /* Increased from 8px */
}

.welcome-message {
  position: relative;
  z-index: 2;
  text-align: center;
  opacity: 0;
  transform: translateY(20px);
  margin-top: 15px; /* Added margin */
}

.welcome-text {
  font-size: 20px; /* Increased from 18px */
  font-weight: 500;
  color: var(--text-color);
  opacity: 0;
  transform: translateY(20px);
  letter-spacing: 0.5px; /* Added letter spacing */
  text-shadow: 0 0 10px rgba(0, 216, 224, 0.4); /* Added text shadow */
  background: linear-gradient(to right, var(--text-color), var(--primary-light) 50%, var(--text-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  padding: 5px 15px; /* Added padding */
  border-radius: 20px; /* Added border radius */
  position: relative;
  display: inline-block;
}

.welcome-text::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 166, 192, 0.1);
  border-radius: 20px;
  z-index: -1;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(0, 166, 192, 0.2);
}

.loading-indicator-animation {
  display: flex;
  justify-content: center;
  gap: 8px; /* Increased from 6px */
  margin-top: 20px; /* Increased from 10px */
  opacity: 0;
}

.dot {
  width: 10px; /* Increased from 8px */
  height: 10px; /* Increased from 8px */
  background-color: var(--primary-color);
  border-radius: 50%;
  display: inline-block;
  box-shadow: 0 0 12px rgba(0, 216, 224, 0.6); /* Enhanced glow */
}

.welcome-message p {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 0;
  padding: 0;
}

html {
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius);
  overflow: hidden;
  background-color: var(--bg-color);
  box-shadow: var(--shadow);
}

:root {
  /* Enhanced Black and Cyan color scheme */
  --primary-color: #00a6c0; /* Bright Cyan */
  --primary-light: #00d8e0; /* Lighter Cyan - brightened */
  --primary-dark: #007a8f; /* Darker Cyan */
  --primary-gradient: linear-gradient(135deg, #00a6c0, #00d8e0);
  --primary-gradient-alt: linear-gradient(135deg, #007a8f, #00a6c0);
  --secondary-color: #1a2a36; /* Dark Blue-Gray */
  --secondary-light: #283b48; /* Medium Blue-Gray */
  --secondary-dark: #0f1923; /* Very Dark Blue-Gray */
  --accent-color: #00d8e0; /* Lighter Cyan for accents */
  --accent-glow: #00f0ff; /* Bright Cyan for glows */
  --text-color: #FFFFFF; /* Pure White */
  --text-light: #F2F4F7; /* Fog Gray */
  --text-secondary: #a0b0c0; /* Muted Blue-Gray */
  --bg-color: #0a0f14; /* Almost Black - darkened */
  --bg-light: #1a2a36; /* Dark Blue-Gray */
  --bg-dark: #050a0f; /* Deep Black - darkened */
  --surface-color: #1a2a36; /* Dark Blue-Gray */
  --bg-gradient: linear-gradient(135deg, #050a0f, #0a1520);
  --bg-gradient-alt: linear-gradient(135deg, #0a0f14, #1a2a36);
  --success-color: #00d8e0; /* Lighter Cyan */
  --warning-color: #ffc107; /* Amber */
  --danger-color: #ff6b6b; /* Coral Red */
  --black: #050a0f; /* Deep Black */
  --white: #FFFFFF; /* Pure White */
  --border-radius: 16px;
  --border-radius-sm: 8px;
  --shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  --shadow-hover: 0 12px 28px rgba(0, 0, 0, 0.4);
  --shadow-inset: inset 0 2px 6px rgba(0, 0, 0, 0.3);
  --shadow-glow: 0 0 20px rgba(0, 166, 192, 0.3);
  --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  --transition-fast: all 0.15s cubic-bezier(0.25, 0.8, 0.25, 1);
  --border-color: rgba(0, 166, 192, 0.2); /* Enhanced cyan border */
  --glow-effect: rgba(0, 216, 224, 0.4); /* Enhanced Cyan glow */
  --glass-border: 1px solid rgba(0, 166, 192, 0.15); /* Enhanced cyan border */
  --glass-background: rgba(26, 42, 54, 0.3); /* Dark Blue-Gray with transparency */
  --backdrop-blur: blur(12px);
  --input-bg: rgba(10, 15, 20, 0.7); /* Darker background for inputs */
  --input-glow: 0 0 16px rgba(0, 216, 224, 0.3); /* Enhanced Cyan glow */
  --input-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  /* Animation classes */
  --animate-shimmer: shimmer 2s infinite linear;
  --animate-pulse-slow: pulse-slow 3s infinite ease-in-out;
  --animate-glow: glow 4s infinite alternate;
  /* Font stacks */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-secondary: 'Inter', 'Montserrat', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-mono: 'IBM Plex Mono', 'JetBrains Mono', 'Roboto Mono', 'Berkeley Mono', 'Consolas', 'Monaco', 'Courier New', monospace;
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

body {
  font-family: var(--font-primary);
  background-color: var(--bg-dark); /* Deep Black */
  background-image:
    radial-gradient(circle at bottom center, rgba(0, 216, 224, 0.15) 0%, transparent 70%), /* Enhanced Cyan Glow */
    radial-gradient(circle at 15% 50%, rgba(0, 216, 224, 0.1) 0%, transparent 50%), /* Enhanced Cyan */
    radial-gradient(circle at 85% 30%, rgba(0, 166, 192, 0.12) 0%, transparent 60%), /* Bright Cyan */
    linear-gradient(135deg, #050a0f, #0a1520); /* Enhanced Black to Dark Blue-Gray gradient */
  background-attachment: fixed;
  color: var(--text-color); /* Pure White */
  width: 400px;
  height: 600px;
  overflow: hidden;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  border-radius: var(--border-radius);
  min-width: 300px;
  min-height: 400px;
  box-shadow: 0 12px 36px rgba(0, 0, 0, 0.7);
  position: relative;
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  border: 1px solid rgba(0, 166, 192, 0.15); /* Enhanced cyan border */
  transition: var(--transition);
}

body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at bottom center, rgba(0, 216, 224, 0.25) 0%, transparent 70%), /* Enhanced Cyan */
    radial-gradient(circle at 80% 70%, rgba(0, 216, 224, 0.2) 0%, transparent 60%); /* Enhanced Cyan */
  opacity: 0.8;
  z-index: -1;
  animation: var(--animate-pulse-slow);
  filter: blur(2px);
}

body::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  width: 200%;
  height: 200%;
  background: transparent;
  opacity: 0.4;
  z-index: -2;
  animation: gradient-shift 25s ease infinite;
  background:
    radial-gradient(circle at bottom center, rgba(0, 216, 224, 0.15) 0%, transparent 70%), /* Enhanced Cyan */
    radial-gradient(circle at 70% 60%, rgba(0, 216, 224, 0.12) 0%, transparent 60%), /* Enhanced Cyan */
    linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(10, 15, 20, 0.9) 100%); /* Enhanced Black to Dark Blue-Gray */
  pointer-events: none;
  filter: blur(1px);
}

@keyframes gradient-shift {
  0% {
    transform: translate(0%, 0%);
  }
  25% {
    transform: translate(-5%, 5%);
  }
  50% {
    transform: translate(-10%, 0%);
  }
  75% {
    transform: translate(-5%, -5%);
  }
  100% {
    transform: translate(0%, 0%);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: var(--bg-dark);
  box-shadow: var(--shadow);
  color: var(--text-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  border: var(--glass-border);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  background-image:
    radial-gradient(circle at bottom center, rgba(0, 216, 224, 0.15) 0%, transparent 70%), /* Enhanced Cyan Glow */
    radial-gradient(circle at 15% 50%, rgba(0, 216, 224, 0.1) 0%, transparent 50%), /* Enhanced Cyan */
    radial-gradient(circle at 85% 30%, rgba(0, 166, 192, 0.12) 0%, transparent 60%), /* Bright Cyan */
    linear-gradient(135deg, #050a0f 0%, #0a1520 100%); /* Enhanced Black to Dark Blue-Gray Gradient */
  position: relative;
  max-height: 600px; /* Match body height */
  animation: fadeInBlur 0.5s ease-out forwards;
  transition: var(--transition);
}

/* Add new glow animation */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(0, 216, 224, 0.3);
  }
  100% {
    box-shadow: 0 0 20px rgba(0, 216, 224, 0.5);
  }
}

/* Add new pulse animation */
@keyframes pulse-slow {
  0% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.02); }
  100% { opacity: 0.7; transform: scale(1); }
}

/* Add new fade in with blur animation */
@keyframes fadeInBlur {
  0% {
    opacity: 0;
    filter: blur(10px);
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    filter: blur(0);
    transform: scale(1);
  }
}

/* Header Styles */
header {
  background-color: rgba(5, 10, 15, 0.9); /* Enhanced Dark Background with transparency */
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  color: var(--text-color);
  padding: 18px 18px 22px;
  position: sticky;
  top: 0;
  z-index: 100;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  margin-bottom: 0;
  border-bottom: 1px solid rgba(0, 216, 224, 0.15);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px -8px rgba(0, 0, 0, 0.6);
  background-image: linear-gradient(to bottom, rgba(5, 10, 15, 0.95), rgba(5, 8, 12, 0.9)); /* Enhanced Dark Background gradient */
  transition: var(--transition);
}

header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 216, 224, 0.5), transparent); /* Enhanced Cyan */
  z-index: 1;
  opacity: 0.8;
  animation: shimmer 3s infinite linear;
}

header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 216, 224, 0.2), transparent); /* Enhanced Cyan */
  z-index: 1;
  opacity: 0.8;
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: 200px 0; }
}

header h1 {
  font-size: 1.4rem;
  margin-bottom: 10px;
  text-align: center;
  font-weight: 600;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--text-color);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-family: var(--font-primary);
}

.logo-img {
  width: 24px;
  height: 24px;
  border-radius: var(--border-radius-sm);
}

.tabs {
  display: flex;
  justify-content: space-around;
  margin-top: 15px;
  margin-bottom: 5px;
  background-color: rgba(10, 10, 10, 0.7); /* Dark Background with transparency */
  border-radius: 16px;
  padding: 5px;
  box-shadow: var(--shadow-inset);
  position: relative;
  border: var(--glass-border);
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.tab-btn {
  background-color: transparent;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  font-size: 0.85rem;
  padding: 10px 12px;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  border-radius: 12px;
  flex: 1;
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.3px;
  position: relative;
  overflow: hidden;
  font-family: var(--font-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin: 0 3px;
}

.tab-btn:hover {
  color: var(--text-color);
  background-color: rgba(255, 255, 255, 0.08);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.tab-btn.active {
  color: var(--white);
  background: var(--primary-gradient);
  box-shadow: 0 4px 12px rgba(30, 185, 128, 0.3); /* Vibrant Green */
  font-weight: 600;
  transform: translateY(-1px);
  position: relative;
  z-index: 1;
}

.tab-btn.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0));
  border-radius: 12px;
  opacity: 0.8;
  z-index: -1;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: var(--primary-light);
  border-radius: 3px;
  box-shadow: 0 0 8px var(--primary-light);
}

.tab-btn i {
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.tab-btn:hover i {
  transform: scale(1.1);
}

.tab-btn.active i {
  transform: scale(1.1);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Main Content Styles */
main {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 0 10px;
  background-color: var(--bg-color);
  position: relative;
  max-height: calc(100% - 120px); /* Adjust based on header height */
}

.tab-content {
  display: none;
  padding: 15px 5px;
  overflow-y: auto;
  height: 100%;
  max-height: calc(100vh - 150px); /* Ensure content stays within viewport */
  scrollbar-width: thin;
  scrollbar-color: var(--primary-light) var(--bg-light);
  box-sizing: border-box;
}

/* Special styling for chat tab to prevent double scrolling */
#chatContent.tab-content {
  overflow-y: hidden;
}

.tab-content::-webkit-scrollbar {
  width: 6px;
}

.tab-content::-webkit-scrollbar-track {
  background: var(--bg-light);
}

.tab-content::-webkit-scrollbar-thumb {
  background-color: var(--primary-light);
  border-radius: 20px;
}

.tab-content.active {
  display: flex;
  flex-direction: column;
  animation: tabFadeIn 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow-y: auto;
  max-height: calc(100vh - 150px); /* Ensure content stays within viewport */
  position: relative;
}

@keyframes tabFadeIn {
  0% { opacity: 0; transform: translateY(8px); }
  100% { opacity: 1; transform: translateY(0); }
}

/* Special styling for chat tab content */
#chatContent.active {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden; /* Prevent double scrollbars */
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Chat Tab Styles */
.provider-selector {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  background-color: transparent;
  padding: 8px 10px;
  border-radius: var(--border-radius-sm);
  position: relative;
  overflow: visible;
  z-index: 5;
  min-height: 50px;
  gap: 8px;
  row-gap: 10px;
}

/* Special class for when OpenRouter is selected */
.provider-selector.openrouter-active {
  height: auto;
  min-height: 300px;
}

/* Remove the gradient line at the top */

#providerSelector {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--primary-color);
  border-radius: var(--border-radius-sm);
  background-color: var(--surface-color);
  margin-right: 0;
  font-size: 0.9rem;
  color: var(--text-color);
  font-weight: 500;
  transition: var(--transition);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url('data:image/svg+xml;utf8,<svg fill="white" height="20" viewBox="0 0 24 24" width="20" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>');
  background-repeat: no-repeat;
  background-position: right 10px center;
  cursor: pointer;
  height: auto;
  min-height: 38px;
  min-width: 150px;
  max-width: 65%;
  order: 1;
  font-family: var(--font-primary);
}

#providerSelector:hover {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 1px var(--primary-light);
}

#providerSelector:focus,
.model-selector:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 1px var(--primary-color);
  background-color: var(--bg-light);
}

.model-selector {
  flex: 1 0 100%;
  padding: 10px 16px;
  border: 2px solid #3182ce;
  border-radius: 4px;
  background-color: #2d3748;
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1rem;
  color: white;
  font-weight: 500;
  transition: var(--transition);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url('data:image/svg+xml;utf8,<svg fill="white" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>');
  background-repeat: no-repeat;
  background-position: right 12px center;
  cursor: pointer;
  width: 100%;
  order: 2;
  box-shadow: 0 0 10px rgba(49, 130, 206, 0.3);
  height: auto;
  min-height: 200px; /* Increased height to show more options */
  display: block !important; /* Force display */
  z-index: 100;
  position: relative;
}

.model-selector:hover {
  border-color: #4299e1;
  box-shadow: 0 0 15px rgba(49, 130, 206, 0.5);
  background-color: #2c3748;
}

.model-selector optgroup {
  background-color: #222;
  color: #aaa;
  font-style: italic;
  font-weight: 600;
  padding: 8px;
}

.model-selector option {
  background-color: #2a2a2a;
  color: white;
  padding: 12px;
  font-weight: 500;
  border-bottom: 1px solid #333;
}

.model-selector optgroup {
  font-weight: 700;
  color: #90cdf4;
  background-color: #1a202c;
  padding: 10px;
  border-bottom: 2px solid #2c5282;
}

.model-selector optgroup[label="Free Models (No API Key Required)"] {
  color: #9ae6b4;
  background-color: #1c2a1c;
  border-bottom: 2px solid #38a169;
}

.model-selector optgroup[label="OpenAI Models"] {
  color: #9decf9;
  background-color: #1a2c35;
  border-bottom: 2px solid #0bc5ea;
}

.model-selector optgroup[label="Anthropic Models"] {
  color: #fbd38d;
  background-color: #2d2a1c;
  border-bottom: 2px solid #dd6b20;
}

.model-selector optgroup[label="Google Models"] {
  color: #e9d8fd;
  background-color: #271f35;
  border-bottom: 2px solid #805ad5;
}

.model-selector option:checked,
.model-selector option:hover,
.model-selector option.selected-option {
  background-color: #3182ce;
  color: white;
  font-weight: 600;
}

/* Dropdown option styling */
#providerSelector option {
  background-color: #2a2a2a;
  color: white;
  padding: 12px;
  font-weight: 500;
  border-bottom: 1px solid #333;
}

#providerSelector optgroup {
  font-weight: 700;
  color: #90cdf4;
  background-color: #1a202c;
  padding: 10px;
  border-bottom: 2px solid #2c5282;
}

#providerSelector optgroup[label="Standard Models"] {
  color: #9decf9;
  background-color: #1a2c35;
  border-bottom: 2px solid #0bc5ea;
}

#providerSelector optgroup[label="Premium Models"] {
  color: #fbd38d;
  background-color: #2d2a1c;
  border-bottom: 2px solid #dd6b20;
}

#providerSelector optgroup[label="Specialized Models"] {
  color: #bee3f8;
  background-color: #1a365d;
  border-bottom: 2px solid #3182ce;
}

/* Fix for dropdown styling in different browsers */
@-moz-document url-prefix() {
  #providerSelector {
    background-color: #2a2a2a;
    padding: 8px 12px;
  }
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  #providerSelector {
    background-color: #2a2a2a;
  }

  select#providerSelector {
    text-indent: 5px;
  }
}

#providerSelector option:checked,
#providerSelector option:hover,
#providerSelector option.selected-option {
  background-color: #4361ee;
  color: white;
  font-weight: 600;
}

/* Actions Dropdown Styles */
.actions-dropdown-container {
  position: relative;
  margin-left: auto;
  order: 3;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 15px;
  z-index: 999999;
}

/* Simple action icons */
.action-icon {
  color: var(--text-color);
  font-size: 1.4rem;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.7;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
}

.action-icon:hover {
  opacity: 1;
  transform: translateY(-1px);
  color: var(--primary-color);
}

/* Specific icon colors */
#actionsDropdownBtn {
  color: var(--text-color);
}

#toggleFloatingChat {
  color: #3498db;
}

#sidebarToggleBtn {
  color: #9b59b6;
}

#clearChat {
  color: #e74c3c;
}

.actions-dropdown-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  background: var(--primary-gradient);
  color: var(--text-color);
  font-size: 1.2rem;
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
  z-index: 1000000;
}

.actions-dropdown-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  border-radius: 50%;
  opacity: 0.5;
}

.actions-dropdown-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.4);
}

.actions-dropdown-btn.has-active {
  position: relative;
}

.actions-dropdown-btn.has-active::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background-color: var(--secondary-color);
  border-radius: 50%;
  border: 2px solid var(--bg-dark);
  animation: pulse 2s infinite;
}

/* Trash Button Styles */
.trash-btn {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  background: linear-gradient(135deg, var(--danger-color), #b83b4b);
  color: var(--text-color);
  font-size: 1rem;
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}

.trash-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  border-radius: 50%;
  opacity: 0.5;
}

.trash-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(231, 76, 60, 0.4);
}

/* Floating Chat Button Styles */
.floating-chat-btn, .sidebar-btn {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: var(--text-color);
  font-size: 1rem;
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
  margin: 0 8px;
}

.floating-chat-btn::before, .sidebar-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  border-radius: 50%;
  opacity: 0.5;
}

.floating-chat-btn:hover, .sidebar-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.4);
}

.floating-chat-btn.active {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  box-shadow: 0 0 10px rgba(46, 204, 113, 0.6);
}

/* Floating Chat Container Styles */
.floating-chat-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 350px;
  background-color: rgba(30, 30, 30, 0.9);
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 10000;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  max-height: 450px;
}

.floating-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: rgba(0, 0, 0, 0.7);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.floating-chat-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.floating-chat-title img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.floating-chat-title div {
  display: flex;
  flex-direction: column;
}

.floating-chat-subtitle {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.6);
}

.floating-chat-close {
  background-color: rgba(231, 76, 60, 0.8);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.floating-chat-close:hover {
  background-color: rgba(231, 76, 60, 1);
}

.floating-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
}

.floating-chat-message {
  padding: 8px 12px;
  border-radius: 12px;
  max-width: 85%;
  word-break: break-word;
  font-size: 0.9rem;
  line-height: 1.4;
}

.floating-chat-message.user {
  background-color: rgba(0, 136, 255, 0.2);
  color: white;
  align-self: flex-end;
  border-bottom-right-radius: 4px;
}

.floating-chat-input-container {
  padding: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.floating-chat-input {
  width: 100%;
  padding: 10px 12px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(45, 45, 45, 0.8);
  color: white;
  font-size: 0.9rem;
  resize: none;
  outline: none;
  transition: all 0.2s ease;
}

.floating-chat-input:focus {
  border-color: rgba(0, 136, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 136, 255, 0.2);
}

.floating-chat-controls {
  display: flex;
  justify-content: space-between;
  padding: 0 10px 10px;
}

.floating-chat-control-button {
  background-color: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 5px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-chat-control-button:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

.floating-chat-up-button {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 136, 255, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  opacity: 0.7;
}

.floating-chat-up-button:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.sidebar-btn {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.sidebar-btn:hover {
  box-shadow: 0 4px 8px rgba(155, 89, 182, 0.4);
}

/* Highlight pulse animation for the actions dropdown button */
@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 20px 10px rgba(67, 97, 238, 0.5);
    transform: scale(1.1);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
    transform: scale(1);
  }
}

.actions-dropdown-btn.highlight-pulse {
  animation: highlight-pulse 1.5s ease-in-out infinite;
}

.actions-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background-color: rgba(30, 30, 30, 0.95);
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.7);
  z-index: 9999999; /* Increased z-index to ensure visibility */
  display: none;
  margin-top: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeIn 0.2s ease-out;
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  transform: translateZ(0);
  will-change: transform;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  pointer-events: auto !important; /* Ensure clicks are registered */
}

.actions-dropdown-menu.show {
  display: block;
  animation: fadeIn 0.2s ease-out;
}

.dropdown-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: var(--transition);
  color: rgba(255, 255, 255, 0.9);
  position: relative;
  z-index: 1000002;
  background-color: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  font-weight: 400;
  font-size: 14px;
}

.dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Switch to Gemini button styling */
#switchToGeminiBtn {
  background-color: rgba(0, 180, 216, 0.15);
  border-left: 3px solid var(--primary-color);
}

#switchToGeminiBtn:hover {
  background-color: rgba(0, 180, 216, 0.25);
}

#switchToGeminiBtn i {
  color: var(--primary-color);
}

.dropdown-item.active {
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  position: relative;
}

.dropdown-item.active::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--primary-color);
}

.dropdown-item i {
  width: 20px;
  text-align: center;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
}

.dropdown-item span {
  font-size: 0.95rem;
  font-weight: 400;
  flex: 1;
}

/* Add keyboard shortcut styling */
.dropdown-item::after {
  content: attr(data-shortcut);
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
  opacity: 0.7;
  margin-left: auto;
  font-family: monospace;
}

.dropdown-divider {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.08);
  margin: 4px 16px;
  border: none;
}

/* Dropdown categories removed for cleaner UI */

.dropdown-item.danger {
  color: var(--danger-color);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dropdown-item.danger:hover {
  background-color: var(--danger-color);
  color: white;
  font-weight: 600;
}

.action-btn {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  background: var(--primary-gradient);
  color: white;
  font-size: 1.1rem;
  box-shadow: 0 2px 8px rgba(67, 97, 238, 0.3);
  position: relative;
  overflow: hidden;
  margin: 2px; /* Add small margin */
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  border-radius: 50%;
  opacity: 0.5;
}

.action-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.4);
}

.translate-btn {
  background: var(--primary-gradient);
}

.translate-btn:hover {
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.5);
}

.scan-btn {
  background: linear-gradient(135deg, #4cc9f0, #3a86ff);
}

.scan-btn:hover {
  box-shadow: 0 4px 12px rgba(76, 201, 240, 0.5);
}

.scan-btn.active {
  background: linear-gradient(135deg, #00b4d8, #0077b6);
  box-shadow: 0 0 10px rgba(0, 180, 216, 0.7);
  animation: pulse 2s infinite;
}

.pdf-btn {
  background: linear-gradient(135deg, #e63946, #d62828);
}

.pdf-btn:hover {
  box-shadow: 0 4px 12px rgba(230, 57, 70, 0.5);
}

.pdf-btn.active {
  background: linear-gradient(135deg, #d00000, #9d0208);
  box-shadow: 0 0 10px rgba(208, 0, 0, 0.7);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 180, 216, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 180, 216, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 180, 216, 0);
  }
}



.clear-btn {
  background: linear-gradient(135deg, var(--danger-color), #c0392b);
}

.clear-btn:hover {
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.5);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  background-color: rgba(5, 10, 15, 0.75);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border-radius: 24px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.35);
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 24px; /* Increased spacing between messages */
  scrollbar-width: thin;
  scrollbar-color: var(--primary-light) var(--bg-dark);
  /* Set a fixed height to allow scrolling while keeping input visible */
  height: 450px;
  max-height: calc(100% - 70px); /* Leave room for input and provider selector */
  border: 1px solid rgba(0, 216, 224, 0.15);
  background-image:
    radial-gradient(circle at bottom center, rgba(0, 216, 224, 0.12) 0%, transparent 70%),
    radial-gradient(circle at top right, rgba(0, 216, 224, 0.06) 0%, transparent 50%),
    radial-gradient(circle at bottom left, rgba(0, 166, 192, 0.06) 0%, transparent 50%),
    linear-gradient(to top, rgba(5, 10, 15, 0.95) 0%, rgba(10, 15, 20, 0.85) 100%);
  position: relative;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.chat-messages::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(30, 185, 128, 0.3), transparent);
  opacity: 0.7;
}

.chat-messages::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120px;
  background: radial-gradient(ellipse at bottom center, rgba(30, 185, 128, 0.2), transparent);
  pointer-events: none;
  z-index: 1;
}

/* System Message Styles */
.system-message {
  padding: 8px 12px;
  margin: 5px auto;
  background-color: rgba(0, 136, 255, 0.15);
  border-left: 3px solid var(--primary-color);
  border-radius: 4px;
  font-size: 0.85rem;
  color: #e2e8f0;
  max-width: 90%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease-in-out;
}

.system-message i {
  color: var(--primary-color);
  font-size: 1.2rem;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(5, 10, 15, 0.5);
  border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--primary-dark), var(--primary-light));
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: padding-box;
  box-shadow: 0 0 8px rgba(0, 216, 224, 0.5);
  transition: var(--transition);
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
  box-shadow: 0 0 12px rgba(0, 216, 224, 0.7);
}

.message {
  display: flex;
  margin-bottom: 22px;
  max-width: 85%;
  animation: messageAppear 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
  border-radius: var(--border-radius);
  transition: var(--transition);
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

/* Add staggered animation for consecutive messages */
.message + .message {
  animation-delay: 0.1s;
}

.message + .message + .message {
  animation-delay: 0.2s;
}

@keyframes messageAppear {
  0% { opacity: 0; transform: translateY(25px); filter: blur(5px); }
  50% { opacity: 0.7; transform: translateY(-6px); filter: blur(0); }
  70% { opacity: 0.85; transform: translateY(4px); }
  85% { opacity: 0.95; transform: translateY(-2px); }
  100% { opacity: 1; transform: translateY(0); filter: blur(0); }
}

@keyframes fadeInBlur {
  0% { opacity: 0; filter: blur(8px); transform: scale(0.98); }
  100% { opacity: 1; filter: blur(0); transform: scale(1); }
}

@keyframes glowPulse {
  0% { box-shadow: 0 0 5px rgba(0, 216, 224, 0.3); }
  50% { box-shadow: 0 0 15px rgba(0, 216, 224, 0.5); }
  100% { box-shadow: 0 0 5px rgba(0, 216, 224, 0.3); }
}

.container {
  animation: fadeInBlur 0.5s ease-out forwards;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
  margin-left: auto;
}

.ai-message {
  align-self: flex-start;
  margin-right: auto;
  width: auto;
  box-sizing: border-box;
}

.message-avatar {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
  padding: 0;
  background: transparent;
  position: relative;
  z-index: 1;
}

.avatar-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 0;
  border-radius: 40%;
  display: block;
}

.logo-icon {
  font-size: 28px;
  color: black;
}

.oval-icon {
  width: 28px;
  height: 28px;
}

.infinity-logo {
  width: 44px;
  height: 44px;
}

.user-message .message-avatar i {
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.message-content {
  background-color: rgba(45, 45, 45, 0.3);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  padding: 20px 24px;
  border-radius: 22px;
  position: relative;
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);
  line-height: 1.7;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: messageFadeIn 0.4s ease-out forwards;
  font-family: var(--font-primary);
  overflow: hidden;
  letter-spacing: 0.3px;
  background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  font-feature-settings: "liga" 0, "calt" 0;
  font-weight: 400;
}

.message-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  opacity: 0.7;
}

@keyframes messageFadeIn {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

.message-content:hover {
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.user-message .message-content {
  background: linear-gradient(135deg, #00a6c0, #00d8e0);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  color: var(--white);
  border-top-right-radius: 8px;
  box-shadow: 0 12px 28px rgba(0, 216, 224, 0.35);
  border: 1px solid rgba(255, 255, 255, 0.25);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.3px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  font-weight: 500;
  animation: user-message-appear 0.5s cubic-bezier(0.19, 1, 0.22, 1) forwards;
}

@keyframes user-message-appear {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    box-shadow: 0 5px 15px rgba(0, 216, 224, 0.2);
  }
  50% {
    opacity: 0.9;
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 216, 224, 0.4);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    box-shadow: 0 12px 28px rgba(0, 216, 224, 0.35);
  }
}

.prompt-copy-btn {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  background: #4361ee;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
  font-size: 0.85rem;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.prompt-copy-btn i {
  margin-right: 5px;
}

.prompt-copy-btn:hover {
  background: #3a56d4;
}

.prompt-copy-btn.success {
  background: #2ecc71;
}

.user-message .message-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  opacity: 0.7;
  pointer-events: none;
}

.user-message .message-content::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  pointer-events: none;
}

.user-message .message-content:hover {
  box-shadow: 0 15px 35px rgba(0, 216, 224, 0.45);
  transform: translateY(-3px) scale(1.01);
  background: linear-gradient(135deg, #00b8d4, #00e5ff);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.ai-message .message-content {
  background-color: rgba(5, 10, 15, 0.65);
  color: var(--text-color);
  border-top-left-radius: 8px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.25);
  position: relative;
  overflow: hidden;
  background-image: linear-gradient(135deg, rgba(10, 15, 20, 0.65), rgba(5, 10, 15, 0.65));
  border: 1px solid rgba(0, 216, 224, 0.2);
  letter-spacing: 0.3px;
  line-height: 1.7;

  /* Enhanced cyan accent */
  border-left: 3px solid rgba(0, 216, 224, 0.6);

  /* Add enhanced glow effect */
  animation: subtle-glow 4s infinite alternate cubic-bezier(0.455, 0.03, 0.515, 0.955);
}

/* Add a subtle border animation for AI messages */
.ai-message .message-content::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid rgba(0, 216, 224, 0);
  border-radius: inherit;
  pointer-events: none;
  animation: border-pulse 3s infinite alternate;
}

@keyframes border-pulse {
  0% { border-color: rgba(0, 216, 224, 0); }
  100% { border-color: rgba(0, 216, 224, 0.3); }
}

/* Error message styling */
.message.ai.error {
  background-color: rgba(207, 102, 121, 0.1);
  border-left: 3px solid var(--danger-color);
}

.message.ai.error .message-content {
  background-color: rgba(207, 102, 121, 0.1);
  background-image: linear-gradient(to bottom, rgba(207, 102, 121, 0.1), rgba(207, 102, 121, 0.05));
  border-left: 3px solid var(--danger-color);
  color: #f8d7da;
}

.message.ai.error strong {
  color: #ff8c9e;
}

/* Safety filter error styling */
.message.ai.safety-error {
  background-color: rgba(255, 165, 0, 0.08);
  border-left: 3px solid #ff9500;
}

.message.ai.safety-error .message-content {
  background-color: rgba(255, 165, 0, 0.08);
  background-image: linear-gradient(to bottom, rgba(255, 165, 0, 0.08), rgba(255, 165, 0, 0.04));
  border-left: 3px solid #ff9500;
  color: #f5f5f5;
}

.message.ai.safety-error .error-header {
  color: #ff9500;
  font-weight: bold;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.message.ai.safety-error .error-content {
  margin-bottom: 12px;
}

.safety-help-button {
  margin-top: 10px;
}

.safety-help-btn {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  color: #f5f5f5;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.safety-help-btn:hover {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Safety help message styling */
.message.ai.safety-help {
  background-color: rgba(0, 122, 255, 0.05);
  border-left: 3px solid #007aff;
}

.message.ai.safety-help .message-content {
  background-color: rgba(0, 122, 255, 0.05);
  background-image: linear-gradient(to bottom, rgba(0, 122, 255, 0.05), rgba(0, 122, 255, 0.02));
  border-left: 3px solid #007aff;
}

.message.ai.safety-help .help-header {
  color: #007aff;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.message.ai.safety-help ul,
.message.ai.safety-help ol {
  margin-left: 20px;
  margin-bottom: 10px;
}

.message.ai.safety-help li {
  margin-bottom: 5px;
}

.ai-message .message-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 166, 192, 0.3), transparent);
  opacity: 0.7;
  animation: var(--animate-shimmer);
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: 200px 0; }
}

@keyframes pulse-slow {
  0% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.02); }
  100% { opacity: 0.7; transform: scale(1); }
}

@keyframes subtle-glow {
  0% { box-shadow: 0 12px 32px rgba(0, 0, 0, 0.25), 0 0 0 rgba(0, 216, 224, 0.1); }
  25% { box-shadow: 0 12px 32px rgba(0, 0, 0, 0.25), 0 0 10px rgba(0, 216, 224, 0.15); }
  50% { box-shadow: 0 12px 32px rgba(0, 0, 0, 0.25), 0 0 20px rgba(0, 216, 224, 0.25); }
  75% { box-shadow: 0 12px 32px rgba(0, 0, 0, 0.25), 0 0 10px rgba(0, 216, 224, 0.15); }
  100% { box-shadow: 0 12px 32px rgba(0, 0, 0, 0.25), 0 0 0 rgba(0, 216, 224, 0.1); }
}

/* Enhanced typing animation */
.typing-cursor {
  display: inline-block;
  width: 3px;
  height: 20px;
  background-color: var(--primary-color);
  margin-left: 3px;
  vertical-align: middle;
  animation: cursor-blink 1.2s infinite cubic-bezier(0.68, -0.55, 0.27, 1.55);
  position: relative;
  top: -1px;
  box-shadow: 0 0 8px var(--primary-color);
  border-radius: 2px;
  transform-origin: center;
}

@keyframes cursor-blink {
  0%, 45% {
    opacity: 1;
    transform: scaleY(1);
    box-shadow: 0 0 12px var(--primary-color);
  }
  50%, 95% {
    opacity: 0.3;
    transform: scaleY(0.7);
    box-shadow: 0 0 5px var(--primary-color);
  }
  100% {
    opacity: 1;
    transform: scaleY(1);
    box-shadow: 0 0 12px var(--primary-color);
  }
}

/* Add a typing indicator animation */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background-color: rgba(5, 10, 15, 0.5);
  border-radius: 12px;
  margin-bottom: 10px;
  width: fit-content;
}

.typing-dot {
  width: 6px;
  height: 6px;
  background-color: var(--primary-color);
  border-radius: 50%;
  opacity: 0.7;
  box-shadow: 0 0 5px var(--primary-color);
}

.typing-dot:nth-child(1) {
  animation: typing-dot-bounce 1.4s infinite 0s;
}

.typing-dot:nth-child(2) {
  animation: typing-dot-bounce 1.4s infinite 0.2s;
}

.typing-dot:nth-child(3) {
  animation: typing-dot-bounce 1.4s infinite 0.4s;
}

@keyframes typing-dot-bounce {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.7;
  }
  30% {
    transform: translateY(-4px);
    opacity: 1;
    box-shadow: 0 0 8px var(--primary-color);
  }
}

.ai-message .message-content:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 216, 224, 0.3);
  transform: translateY(-3px) scale(1.01);
  border-color: rgba(0, 216, 224, 0.4);
  background-image: linear-gradient(135deg, rgba(10, 15, 20, 0.7), rgba(5, 10, 15, 0.7));
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.ai-message .message-content:hover::after {
  animation-duration: 1.5s;
  border-color: rgba(0, 216, 224, 0.4);
}

/* Error message styling */
.error-message .message-content {
  border-left: 3px solid #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
  background-image: linear-gradient(to bottom, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.05));
}

.error-message .message-content::before {
  background: linear-gradient(90deg, #e74c3c, transparent);
}

/* Message Action Button */
.message-action-btn {
  position: absolute;
  bottom: -30px;
  right: 10px;
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 10px;
  font-size: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  font-weight: 500;
  z-index: 5;
}

.message-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(67, 97, 238, 0.3);
  background: linear-gradient(135deg, #4895ef, #4361ee);
}

.message-action-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #3a0ca3, #4361ee);
}

.message-action-btn.success {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  box-shadow: 0 1px 4px rgba(46, 204, 113, 0.3);
}

.message-action-btn.error {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  box-shadow: 0 1px 4px rgba(231, 76, 60, 0.3);
}

.message-content p {
  margin-bottom: 8px;
  line-height: 1.5;
}

.message-content p:last-child {
  margin-bottom: 0;
}

/* Code Block Styles */
.code-block-container, .code-container {
  margin: 14px 0;
  border-radius: 12px;
  overflow: hidden;
  background-color: rgba(18, 18, 18, 0.8);
  border: var(--glass-border);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  position: relative;
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
}

.code-block-header, .code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 14px;
  background-color: rgba(30, 30, 30, 0.7);
  border-bottom: var(--glass-border);
  font-family: 'IBM Plex Mono', var(--font-mono);
  letter-spacing: 0.3px;
}

.code-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.brave-copy-btn {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.8rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.brave-copy-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(243, 156, 18, 0.3);
}

.brave-copy-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.code-buttons {
  display: flex;
  gap: 8px;
}

.code-language {
  font-size: 0.85rem;
  color: #bbb;
  font-family: 'IBM Plex Mono', var(--font-mono);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 3px 8px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.insert-code-btn, .copy-code-btn, .simple-copy-btn, .ultra-simple-copy-btn, .copy-button, .direct-copy-btn {
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 0.8rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.copy-code-btn, .simple-copy-btn, .ultra-simple-copy-btn, .copy-button, .direct-copy-btn {
  background: linear-gradient(135deg, #4cc9f0, #3a86ff);
}

/* New copy button style */
.code-container .copy-code-btn,
.code-container .direct-copy-btn,
.copy-button {
  position: absolute;
  top: 6px;
  right: 6px;
  z-index: 10;
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  font-weight: 500;
}

.copy-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(67, 97, 238, 0.3);
  background: linear-gradient(135deg, #4895ef, #4361ee);
}

.copy-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #3a0ca3, #4361ee);
}

.copy-button.success {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  box-shadow: 0 1px 4px rgba(46, 204, 113, 0.3);
}

.copy-button.success:hover {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  box-shadow: 0 2px 6px rgba(46, 204, 113, 0.4);
}

.copy-button.error {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  box-shadow: 0 1px 4px rgba(231, 76, 60, 0.3);
}

.copy-button.error:hover {
  background: linear-gradient(135deg, #c0392b, #e74c3c);
  box-shadow: 0 2px 6px rgba(231, 76, 60, 0.4);
}

.hidden-textarea {
  position: absolute;
  left: -9999px;
  height: 1px;
  width: 1px;
  opacity: 0;
  overflow: hidden;
}

.insert-code-btn:hover, .copy-code-btn:hover, .simple-copy-btn:hover, .ultra-simple-copy-btn:hover, .copy-button:hover, .direct-copy-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.insert-code-btn:active, .copy-code-btn:active, .simple-copy-btn:active, .ultra-simple-copy-btn:active, .copy-button:active, .direct-copy-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.copy-code-btn:hover, .simple-copy-btn:hover, .ultra-simple-copy-btn:hover, .copy-button:hover, .direct-copy-btn:hover {
  box-shadow: 0 3px 6px rgba(76, 201, 240, 0.3);
}

.insert-code-btn.success, .copy-code-btn.success, .simple-copy-btn.success, .ultra-simple-copy-btn.success, .copy-button.success, .direct-copy-btn.success {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.insert-code-btn.error, .copy-code-btn.error, .simple-copy-btn.error, .ultra-simple-copy-btn.error, .copy-button.error, .direct-copy-btn.error {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.message-content pre {
  margin: 0;
  padding: 14px 16px;
  overflow-x: auto;
  background-color: rgba(18, 18, 18, 0.9);
  border-radius: 0 0 10px 10px;
  font-family: 'IBM Plex Mono', var(--font-mono);
  font-size: 0.95rem;
  line-height: 1.6;
  color: #f8f8f2;
  max-height: 400px;
  scrollbar-width: thin;
  scrollbar-color: rgba(85, 85, 85, 0.6) rgba(26, 26, 26, 0.8);
  letter-spacing: 0.3px;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
}

.message-content pre::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.message-content pre::-webkit-scrollbar-track {
  background: rgba(26, 26, 26, 0.6);
  border-radius: 4px;
}

.message-content pre::-webkit-scrollbar-thumb {
  background-color: rgba(85, 85, 85, 0.6);
  border-radius: 4px;
  border: 2px solid rgba(26, 26, 26, 0.6);
}

.message-content code {
  font-family: 'IBM Plex Mono', var(--font-mono);
  background-color: rgba(255, 255, 255, 0.08);
  padding: 3px 5px;
  border-radius: 4px;
  font-size: 0.92em;
  color: #f8f8f2;
  letter-spacing: 0.3px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.chat-input-container {
  display: flex;
  position: relative;
  z-index: 1;
  margin: 10px auto;
  align-items: center;
  width: 75%;
  gap: 8px;
  padding: 0;
  background-color: transparent;
  border-radius: 24px;
  box-shadow: var(--input-shadow);
}

#userInput:focus {
  background-color: var(--input-bg);
  box-shadow: var(--input-glow);
  border-color: var(--primary-color);
  outline: none;
}

#userInput {
  flex: 1;
  border: var(--glass-border);
  outline: none;
  padding: 12px 16px;
  padding-right: 100px; /* Reduced space for buttons */
  resize: none;
  font-family: var(--font-primary);
  font-size: 0.9rem;
  color: var(--text-color);
  line-height: 1.4;
  background-color: var(--input-bg);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  transition: var(--transition);
  min-height: 20px;
  max-height: 80px;
  border-radius: 24px;
  box-shadow: var(--shadow-inset);
  letter-spacing: 0.2px;
  width: 100%;
  font-feature-settings: "liga" 0, "calt" 0;
  position: relative;
  overflow: hidden;
  box-sizing: border-box; /* Ensure padding is included in width calculation */
}

#userInput::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 180, 216, 0.2), transparent);
  opacity: 0.5;
  pointer-events: none;
}

#userInput::placeholder {
  color: var(--text-light);
  opacity: 0.7;
  font-style: italic;
  font-size: 0.85rem;
}

/* Removed duplicate focus style */

.send-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--primary-gradient);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  font-size: 0.9rem;
  box-shadow: var(--shadow);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  margin-right: 6px;
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
}

.send-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  border-radius: 50%;
  opacity: 0.5;
}

.send-btn i {
  position: relative;
  z-index: 2;
}

.send-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--input-glow);
  background: linear-gradient(135deg, #00a2ff, #0077cc);
}

.send-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 136, 255, 0.3);
  background: linear-gradient(135deg, #0066cc, #004c99);
}

.trash-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e74c3c;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  font-size: 0.85rem;
  box-shadow: 0 2px 6px rgba(231, 76, 60, 0.3);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  margin-left: 6px;
}

.trash-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  border-radius: 50%;
  opacity: 0.5;
}

.trash-btn:hover {
  background: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
}

.trash-btn:active {
  background: #a93226;
  transform: translateY(0);
  box-shadow: 0 0 8px rgba(231, 76, 60, 0.3);
}

.suggestion-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
  justify-content: center;
}

.suggestion-chip {
  background-color: rgba(45, 45, 45, 0.4);
  color: var(--text-color);
  border: var(--glass-border);
  border-radius: 20px;
  padding: 10px 16px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 180px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  position: relative;
  letter-spacing: 0.2px;
  background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
}

.suggestion-chip:hover {
  background: var(--primary-gradient);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--input-glow);
  border-color: rgba(0, 136, 255, 0.4);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
}

/* Special action chips styling */
.suggestion-chip:nth-child(5) {
  background: var(--primary-gradient);
  color: white;
  border-color: rgba(0, 136, 255, 0.3);
}

.suggestion-chip:nth-child(5):hover {
  background: linear-gradient(135deg, #4895ef, #4361ee);
  box-shadow: 0 2px 10px rgba(67, 97, 238, 0.4);
  transform: translateY(-2px);
}

.suggestion-chip:nth-child(6) {
  background: linear-gradient(135deg, var(--success-color), #27ae60);
  color: white;
  border-color: rgba(46, 204, 113, 0.3);
}

.suggestion-chip:nth-child(6):hover {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  box-shadow: 0 2px 10px rgba(46, 204, 113, 0.4);
  transform: translateY(-2px);
}

/* Settings Tab Styles */
.api-key-form,
.settings-form {
  background-color: rgba(20, 20, 20, 0.8);
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 25px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  overflow: visible;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  max-width: 100%;
  box-sizing: border-box;
}

.api-key-form:hover,
.settings-form:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.12);
}

.api-key-form::before,
.settings-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  z-index: 1;
}

#settingsContent,
#statsContent,
#historyContent {
  overflow-y: auto;
  max-height: calc(100vh - 180px);
  padding-bottom: 20px;
}

#settingsContent h2,
#statsContent h2 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: var(--text-color);
  position: relative;
  padding-bottom: 8px;
  font-family: var(--font-primary);
}

#settingsContent h2:after,
#statsContent h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--primary-gradient);
  border-radius: 3px;
}

.api-key-input {
  position: relative;
  margin-bottom: 18px;
}

.api-key-input label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
  font-family: var(--font-primary);
}

.api-key-input input {
  width: 100%;
  padding: 12px 14px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding-right: 40px;
  transition: var(--transition);
  font-size: 0.9rem;
  background-color: rgba(45, 45, 45, 0.8);
  color: var(--text-color);
  box-shadow: var(--shadow-inset);
  font-family: var(--font-secondary);
}

.api-key-input input:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 2px rgba(76, 201, 240, 0.2);
  background-color: rgba(61, 61, 61, 0.9);
}

.toggle-visibility {
  position: absolute;
  right: 10px;
  top: 38px;
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
}

.toggle-visibility:hover {
  color: var(--primary-color);
}

.setting-item {
  margin-bottom: 20px;
  position: relative;
  transition: all 0.3s ease;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-item:hover {
  transform: translateY(-1px);
}

.setting-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: var(--font-primary);
  letter-spacing: 0.2px;
}

.setting-item label i {
  color: var(--primary-color);
  font-size: 1rem;
}

.setting-item input[type="number"],
.setting-item input[type="range"] {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  background-color: rgba(30, 30, 30, 0.8);
  color: var(--text-color);
  box-shadow: var(--shadow-inset);
  font-family: var(--font-primary);
  font-size: 0.95rem;
  letter-spacing: 0.2px;
}

.setting-item input[type="number"]:focus,
.setting-item input[type="range"]:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 136, 255, 0.2);
  background-color: rgba(40, 40, 40, 0.9);
}

.setting-item input[type="range"] {
  height: 8px;
  -webkit-appearance: none;
  appearance: none;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  border: none;
  padding: 0;
  margin: 15px 0;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.setting-item input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  border: 2px solid var(--primary-color);
  transition: all 0.2s ease;
}

.setting-item input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 136, 255, 0.5);
}

.setting-item input[type="checkbox"] {
  margin-right: 8px;
  accent-color: var(--primary-color);
  transform: scale(1.3);
  cursor: pointer;
  transition: all 0.2s ease;
}

.setting-item input[type="checkbox"]:hover {
  transform: scale(1.4);
}

.save-btn {
  width: 100%;
  max-width: 300px;
  padding: 14px 20px;
  background: linear-gradient(135deg, var(--success-color), #27ae60);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-weight: 600;
  margin: 25px auto 5px;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 2px 8px rgba(46, 204, 113, 0.3);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.2px;
  font-family: var(--font-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.save-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0.6;
}

.save-btn:hover {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(46, 204, 113, 0.5);
}

.save-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(46, 204, 113, 0.3);
}

.test-btn {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, var(--secondary-color), #0bc5ea);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-weight: 600;
  margin-top: 15px;
  transition: var(--transition);
  box-shadow: 0 2px 8px rgba(76, 201, 240, 0.3);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;
}

.test-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0.6;
}

.test-btn:hover {
  background: linear-gradient(135deg, #0bc5ea, #4cc9f0);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 201, 240, 0.4);
}

.test-btn:active {
  transform: translateY(0);
}

/* Secondary Button Styles */
.secondary-btn {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  box-shadow: 0 2px 8px rgba(0, 166, 192, 0.3);
  position: relative;
  overflow: hidden;
  padding: 10px 16px;
}

.secondary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0.6;
}

.secondary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 166, 192, 0.4);
}

.secondary-btn:active {
  transform: translateY(0);
}

/* History Tab Styles */
.history-container {
  background-color: rgba(20, 20, 20, 0.8);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 20px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  max-height: calc(100vh - 200px); /* Ensure it stays within viewport */
  display: flex;
  flex-direction: column;
  position: relative;
}

.history-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  z-index: 1;
}

.history-list {
  overflow-y: auto;
  padding: 0;
  max-height: calc(100vh - 220px); /* Ensure it stays within container */
  scrollbar-width: thin;
  scrollbar-color: var(--primary-light) rgba(20, 20, 20, 0.5);
  flex: 1;
}

.history-list::-webkit-scrollbar {
  width: 6px;
}

.history-list::-webkit-scrollbar-track {
  background: rgba(20, 20, 20, 0.5);
  border-radius: 10px;
}

.history-list::-webkit-scrollbar-thumb {
  background-color: var(--primary-light);
  border-radius: 10px;
}

.history-item {
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  background-color: rgba(25, 25, 25, 0.5);
}

.history-item:hover {
  background-color: rgba(0, 136, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.history-item-title {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
  font-family: var(--font-primary);
  letter-spacing: 0.2px;
}

.history-item-date {
  font-size: 0.8rem;
  color: var(--primary-light);
  background-color: rgba(0, 136, 255, 0.1);
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.history-item-preview {
  font-size: 0.85rem;
  color: var(--text-light);
  margin-bottom: 8px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 3em;
  font-family: var(--font-primary);
  letter-spacing: 0.2px;
  background-color: rgba(255, 255, 255, 0.03);
  padding: 8px 10px;
  border-radius: 8px;
  border-left: 2px solid rgba(0, 136, 255, 0.3);
}

.history-item-actions {
  display: flex;
  gap: 10px;
  margin-top: 12px;
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateY(5px);
}

.history-item:hover .history-item-actions {
  opacity: 1;
  transform: translateY(0);
}

.history-action-btn {
  background-color: rgba(30, 30, 30, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.history-action-btn:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-color);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.history-action-btn.restore {
  color: var(--primary-color);
  border-color: rgba(0, 136, 255, 0.3);
}

.history-action-btn.restore:hover {
  background-color: rgba(0, 136, 255, 0.15);
  color: var(--primary-color);
  border-color: rgba(0, 136, 255, 0.4);
}

.history-action-btn.delete {
  color: var(--danger-color);
  border-color: rgba(220, 53, 69, 0.3);
}

.history-action-btn.delete:hover {
  background-color: rgba(220, 53, 69, 0.15);
  color: #e74c3c;
  border-color: rgba(220, 53, 69, 0.4);
}

.empty-history-message {
  padding: 40px 20px;
  text-align: center;
  color: var(--text-light);
  font-style: italic;
  font-size: 0.95rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  margin: 20px;
  border: 1px dashed rgba(255, 255, 255, 0.1);
}

.danger-btn {
  width: 100%;
  max-width: 300px;
  padding: 12px 20px;
  background: linear-gradient(135deg, var(--danger-color), #c0392b);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 20px auto 10px;
  font-family: var(--font-primary);
  letter-spacing: 0.2px;
  position: relative;
  overflow: hidden;
}

.danger-btn:hover {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(231, 76, 60, 0.5);
}

.danger-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.danger-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0.6;
  pointer-events: none;
}

/* Stats Tab Styles */
.stats-header {
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 10px;
}

.stats-overview {
  background-color: #222;
  border-radius: var(--border-radius);
  padding: 20px;
  margin-bottom: 25px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Summary Stats */
.stats-summary {
  display: flex;
  gap: 20px;
}

.summary-card {
  flex: 1;
  background-color: #2a2a2a;
  border-radius: var(--border-radius);
  padding: 15px 20px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-label {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-value {
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.section-title {
  font-size: 1.1rem;
  margin: 0 0 15px 0;
  color: var(--text-color);
  font-weight: 600;
}

/* Provider Stats */
.stats-section {
  margin-bottom: 20px;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.stats-card {
  background-color: #2a2a2a;
  padding: 15px;
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  transition: transform 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
}

.stats-card.openai::before {
  background-color: #10a37f;
}

.stats-card.claude::before {
  background-color: #a35f10;
}

.stats-card.deepseek::before {
  background-color: #4361ee;
}

.stats-card.gemini::before {
  background-color: #a310a3;
}



.stats-card.openrouter::before {
  background-color: #3182ce;
}

.provider-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.provider-icon {
  color: var(--text-light);
  margin-right: 10px;
  font-size: 1.1rem;
  width: 24px;
  text-align: center;
}

.stats-card.openai .provider-icon {
  color: #10a37f;
}

.stats-card.claude .provider-icon {
  color: #a35f10;
}

.stats-card.deepseek .provider-icon {
  color: #4361ee;
}

.stats-card.gemini .provider-icon {
  color: #a310a3;
}



.stats-card.openrouter .provider-icon {
  color: #3182ce;
}

.stats-card h4 {
  margin: 0;
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
}

.stats-metrics {
  display: flex;
  justify-content: space-between;
}

.metric {
  text-align: center;
  flex: 1;
  padding: 5px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.metric:first-child {
  margin-right: 8px;
}

.metric-label {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-bottom: 5px;
}

.metric-value {
  font-weight: 600;
  color: #ffffff;
  font-size: 1.1rem;
}

@keyframes pulse {
  0% { box-shadow: 0 0 12px rgba(58, 134, 255, 0.6); }
  50% { box-shadow: 0 0 20px rgba(58, 134, 255, 0.9); }
  100% { box-shadow: 0 0 12px rgba(58, 134, 255, 0.6); }
}

.reset-stats {
  text-align: center;
  margin-top: 25px;
}

.reset-btn {
  padding: 10px 20px;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 0.9rem;
}

.reset-btn i {
  font-size: 0.9rem;
}

.reset-btn:hover {
  background-color: #c0392b;
  transform: translateY(-1px);
  box-shadow: 0 5px 10px rgba(220, 53, 69, 0.4);
}

.reset-btn:active {
  transform: translateY(0);
}

/* Footer Styles */
footer {
  background-color: #181825;
  padding: 10px 20px;
  text-align: center;
  font-size: 0.85rem;
  color: var(--text-light);
  border-top: 1px solid rgba(91, 33, 182, 0.2);
  border-radius: 0 0 16px 16px;
  position: relative;
  overflow: hidden;
}

footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  z-index: 1;
}

#statusMessage {
  transition: var(--transition);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  position: fixed;
  bottom: 70px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 18px;
  border-radius: 20px;
  font-size: 0.9rem;
  z-index: 1000;
  text-align: center;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0;
  transform: translateX(-50%) translateY(10px);
  pointer-events: none;
}

#statusMessage.active {
  display: flex;
  opacity: 1;
  transform: translateX(-50%) translateY(0);
  pointer-events: auto;
}

/* Status message types */
#statusMessage.error {
  background-color: rgba(220, 38, 38, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
}

#statusMessage.success {
  background-color: rgba(16, 185, 129, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

#statusMessage.warning {
  background-color: rgba(245, 158, 11, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

#statusMessage.info {
  background-color: rgba(59, 130, 246, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

#statusMessage.loading {
  background-color: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  animation: statusPulse 2s infinite;
}

/* Status message animations */
#statusMessage.status-animate {
  animation: status-bounce 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

#statusMessage.status-fade-out {
  opacity: 0;
  transform: translateX(-50%) translateY(10px);
}

@keyframes status-bounce {
  0% { transform: translateX(-50%) translateY(10px); opacity: 0; }
  50% { transform: translateX(-50%) translateY(-5px); opacity: 1; }
  100% { transform: translateX(-50%) translateY(0); opacity: 1; }
}

/* Enhanced Loading Animation */
.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  height: 20px;
  position: relative;
}

.loading-dots::after {
  content: '';
  animation: dots 1.8s infinite cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced pulse animation for status message */
@keyframes statusPulse {
  0% { opacity: 0.7; transform: scale(0.98); }
  50% { opacity: 1; transform: scale(1.02); }
  100% { opacity: 0.7; transform: scale(0.98); }
}

@keyframes dots {
  0%, 15% { content: '.'; }
  30%, 45% { content: '..'; }
  60%, 75% { content: '...'; }
  90%, 100% { content: ''; }
}

/* Add a new loading spinner animation */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 216, 224, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spinner 1s infinite ease-in-out;
  margin-right: 8px;
  vertical-align: middle;
}

@keyframes spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Typing Indicator */
.typing-indicator {
  align-self: flex-start;
  background-color: var(--bg-dark);
  padding: 12px 16px;
  border-radius: 18px;
  margin-left: 46px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  animation: fadeIn 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.typing-indicator .dots {
  display: flex;
  align-items: center;
}

.typing-indicator .dot {
  width: 8px;
  height: 8px;
  background-color: var(--secondary-color);
  border-radius: 50%;
  margin-right: 4px;
  animation: typing-dot 1.4s infinite ease-in-out;
  opacity: 0.8;
  box-shadow: 0 0 8px rgba(76, 201, 240, 0.6);
  position: relative;
}

.typing-indicator .dot::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background-color: rgba(76, 201, 240, 0.3);
  border-radius: 50%;
  z-index: -1;
  animation: pulse 1.5s infinite ease-in-out;
}

.typing-indicator .dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-dot {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-5px); }
}

/* Utility Classes */
.hidden {
  display: none;
}

/* Message Formatting */
.message-content pre {
  background-color: rgba(26, 26, 26, 0.9);
  border-radius: var(--border-radius-sm);
  padding: 14px;
  overflow-x: auto;
  margin: 12px 0;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.85rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  backdrop-filter: blur(4px);
}

.message-content pre::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  opacity: 0.7;
  animation: shimmer 2s infinite linear;
}

.message-content code {
  background-color: rgba(45, 45, 45, 0.7);
  padding: 3px 6px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.85rem;
  color: #e0e0e0;
  border: 1px solid rgba(255, 255, 255, 0.05);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.user-message .message-content pre,
.user-message .message-content code {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Result Box Styling - Kept for backward compatibility */
.result-box {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  margin: 14px 0;
  box-shadow: var(--shadow);
  transition: var(--transition);
  background-color: #1e1e1e;
  position: relative;
}

.result-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  z-index: 1;
}

/* Error Box Styling */
.error-box {
  border-color: rgba(231, 76, 60, 0.4);
  background-color: rgba(30, 20, 20, 0.9);
}

.error-box::before {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.error-box .result-title {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
  font-weight: 600;
  border-bottom-color: rgba(231, 76, 60, 0.3);
}

.error-box .result-content {
  color: #f5f5f5;
}

.result-box:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-light);
}

.result-title {
  background-color: #2a2a2a;
  padding: 12px 16px;
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  letter-spacing: 0.3px;
}

.result-content {
  padding: 16px;
  line-height: 1.6;
  color: var(--text-color);
}

/* Copy button styles */
.copy-btn {
  background-color: #4361ee;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 10px;
  margin-top: 10px;
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 500;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.copy-btn i {
  margin-right: 5px;
}

.copy-btn:hover {
  background-color: #3a56d4;
}

.copy-btn.success {
  background-color: #2ecc71;
}

.copy-btn.error {
  background-color: #e74c3c;
}

/* Loading Box - New Style */
.loading-box {
  display: flex;
  align-items: center;
  background-color: rgba(30, 30, 46, 0.6);
  border-radius: 12px;
  padding: 16px;
  margin: 12px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(67, 97, 238, 0.2);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(67, 97, 238, 0.3);
  border-radius: 50%;
  border-top-color: #4361ee;
  animation: spin 1s linear infinite;
  margin-right: 16px;
  flex-shrink: 0;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-message {
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.95rem;
}

/* Legacy Loading Indicator - For Backward Compatibility */
.loading-indicator {
  display: flex;
  align-items: center;
  margin: 12px 0;
  color: var(--text-light);
  font-weight: 500;
}

.loading-indicator .loading-dots {
  margin-left: 8px;
  display: inline-block;
  color: var(--primary-color);
}

.loading-indicator .loading-dots:after {
  content: '.';
  animation: dots 1.5s steps(1, end) infinite;
}

@keyframes dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60% { content: '...'; }
  80%, 100% { content: ''; }
}

/* Language Selection Modal */
.select-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.select-modal-content {
  background-color: #2a2a2a;
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  border: 1px solid #444;
}

.select-modal-content h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: white;
  font-size: 1.2rem;
  text-align: center;
}

.select-search {
  padding: 10px 12px;
  border: 1px solid #444;
  border-radius: 8px;
  background-color: #333;
  color: white;
  margin-bottom: 15px;
  font-size: 0.95rem;
  width: 100%;
  box-sizing: border-box;
}

.select-search:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(58, 134, 255, 0.3);
}

.select-options {
  overflow-y: auto;
  max-height: 300px;
  margin-bottom: 15px;
  border: 1px solid #444;
  border-radius: 8px;
  background-color: #222;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-light) #222;
}

.select-options::-webkit-scrollbar {
  width: 6px;
}

.select-options::-webkit-scrollbar-track {
  background: #222;
  border-radius: 10px;
}

.select-options::-webkit-scrollbar-thumb {
  background-color: var(--primary-light);
  border-radius: 10px;
}

.select-option {
  padding: 10px 15px;
  cursor: pointer;
  transition: var(--transition);
  color: white;
  border-bottom: 1px solid #333;
}

.select-option:last-child {
  border-bottom: none;
}

.select-option:hover {
  background-color: rgba(58, 134, 255, 0.2);
}

.select-option.selected {
  background-color: rgba(58, 134, 255, 0.3);
  font-weight: 600;
}

.no-results {
  padding: 15px;
  text-align: center;
  color: #888;
  font-style: italic;
}

.select-cancel-btn {
  padding: 10px;
  background-color: #444;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
  align-self: center;
  width: 100%;
}

.select-cancel-btn:hover {
  background-color: #555;
  transform: translateY(-2px);
}

/* Translation Box Styling */
.translation-box {
  border: 1px solid rgba(67, 97, 238, 0.3);
  border-radius: var(--border-radius);
  overflow: hidden;
  margin: 14px 0;
  box-shadow: var(--shadow);
  transition: var(--transition);
  background-color: rgba(30, 30, 46, 0.8);
  position: relative;
}

.translation-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4361ee, #4cc9f0);
  z-index: 1;
}

.translation-header {
  background-color: rgba(67, 97, 238, 0.1);
  padding: 12px 16px;
  font-weight: 500;
  border-bottom: 1px solid rgba(67, 97, 238, 0.2);
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.translation-header i {
  color: #4361ee;
  font-size: 1.1rem;
}

.translation-content {
  padding: 16px;
  line-height: 1.6;
  color: var(--text-color);
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.translation-actions {
  padding: 10px 16px;
  border-top: 1px solid rgba(67, 97, 238, 0.1);
  display: flex;
  justify-content: flex-end;
}

.translation-copy-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #4361ee, #3a86ff);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
}

.translation-copy-btn:hover {
  background: linear-gradient(135deg, #4895ef, #4361ee);
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(67, 97, 238, 0.3);
}

.translation-copy-btn:active {
  transform: translateY(0);
}

.translation-copy-btn.success {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.translation-copy-btn.error {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

/* Legacy Copy Translation Button (for backward compatibility) */
.copy-btn {
  display: block;
  margin-top: 15px;
  padding: 8px 12px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
  width: 100%;
}

.copy-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

/* Footer and Status Message Styles */
footer {
  padding: 10px 15px;
  background-color: rgba(10, 10, 10, 0.6);
  border-top: var(--glass-border);
  color: var(--text-light);
  font-size: 0.85rem;
  text-align: center;
  position: relative;
  z-index: 10;
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

#statusMessage {
  color: #aaa;
  transition: var(--transition);
  opacity: 0.7;
}

#statusMessage.active {
  color: #fff;
  opacity: 1;
  font-weight: 500;
}

#statusMessage.loading {
  color: #4361ee;
  opacity: 1;
}

#statusMessage.loading i {
  margin-right: 5px;
  color: #4361ee;
}

#statusMessage.error {
  color: var(--danger-color);
  opacity: 1;
  font-weight: 500;
}

/* Chat Input Container Styles */
.chat-input-container {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: rgba(5, 10, 15, 0.85);
  position: relative;
  margin-bottom: 12px;
  box-sizing: border-box;
  width: 100%;
  max-width: 98%;
  margin-left: auto;
  margin-right: auto;
  border-radius: 24px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(0, 216, 224, 0.2);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  background-image:
    radial-gradient(circle at bottom center, rgba(0, 216, 224, 0.08) 0%, transparent 70%),
    linear-gradient(to bottom, rgba(5, 10, 15, 0.95), rgba(10, 15, 20, 0.85));
  z-index: 10;
}

.chat-input-container:focus-within {
  box-shadow: 0 10px 30px rgba(0, 216, 224, 0.25);
  border: 1px solid rgba(0, 216, 224, 0.4);
  background-color: rgba(5, 10, 15, 0.95);
  transform: translateY(-2px);
  background-image:
    radial-gradient(circle at bottom center, rgba(0, 216, 224, 0.12) 0%, transparent 70%),
    linear-gradient(to bottom, rgba(5, 10, 15, 0.98), rgba(10, 15, 20, 0.9));
}

.chat-input-container::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 10%;
  right: 10%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 216, 224, 0.5), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chat-input-container:focus-within::before {
  opacity: 1;
}

.chat-input-wrapper {
  flex: 1;
  position: relative;
  width: 100%;
}

#userInput {
  flex: 1;
  border: 1px solid rgba(0, 216, 224, 0.25);
  background-color: rgba(5, 10, 15, 0.75);
  color: var(--text-color);
  font-size: 15px;
  padding: 16px 20px;
  padding-right: 130px; /* Increased space for buttons */
  resize: none;
  font-family: var(--font-primary);
  max-height: 120px;
  min-height: 24px;
  overflow-y: auto;
  position: relative;
  z-index: 1; /* Ensure it stays below the buttons */
  width: calc(100% - 10px); /* Adjust width to prevent overflow */
  box-sizing: border-box;
  line-height: 1.6;
  border-radius: 20px;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  background-image: linear-gradient(to bottom, rgba(5, 10, 15, 0.85), rgba(10, 15, 20, 0.75));
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25), inset 0 1px 3px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.3px;
  font-weight: 400;
}

#userInput:focus {
  background-color: rgba(5, 10, 15, 0.85); /* Slightly darker for focus */
  background-image: linear-gradient(to bottom, rgba(5, 10, 15, 0.95), rgba(10, 15, 20, 0.85));
  box-shadow: 0 6px 24px rgba(0, 216, 224, 0.25), inset 0 1px 3px rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 216, 224, 0.5);
  outline: none;
  z-index: 1; /* Ensure it stays below the buttons */
  transform: translateY(-2px);
}

#userInput::placeholder {
  color: rgba(255, 255, 255, 0.45);
  transition: all 0.3s ease;
  font-weight: 300;
  font-style: italic;
  opacity: 0.8;
}

#userInput:focus::placeholder {
  color: rgba(255, 255, 255, 0.3);
  opacity: 0.6;
  transform: translateY(-2px);
}

.chat-input-buttons {
  display: flex;
  align-items: center;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 200; /* Ensure buttons are above the textarea */
  pointer-events: auto; /* Make sure buttons are clickable */
  gap: 8px; /* Add space between buttons */
  background: rgba(15, 20, 30, 0.75); /* Semi-transparent background */
  padding: 6px 10px; /* Add some padding */
  margin: 0; /* Remove any margin */
  width: auto; /* Let width be determined by content */
  height: auto; /* Let height be determined by content */
  border-radius: 16px; /* Rounded corners */
  backdrop-filter: blur(10px); /* Blur effect */
  -webkit-backdrop-filter: blur(10px); /* For Safari */
  border: 1px solid rgba(0, 166, 192, 0.2);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.send-btn {
  background: linear-gradient(135deg, #00a6c0, #00d8e0);
  color: white;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 6px 18px rgba(0, 216, 224, 0.35);
  position: relative;
  overflow: hidden;
  flex-shrink: 0; /* Prevent button from shrinking */
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform-origin: center;
}

.send-btn:hover {
  animation: send-button-hover 2s infinite cubic-bezier(0.455, 0.03, 0.515, 0.955);
  background: linear-gradient(135deg, #00d8e0, #00a6c0);
}

.send-btn:active {
  transform: translateY(0) scale(0.95);
  box-shadow: 0 3px 12px rgba(0, 216, 224, 0.3);
  animation: send-button-click 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes send-button-click {
  0% { transform: scale(0.95); }
  50% { transform: scale(0.85); }
  100% { transform: scale(0.95); }
}

.send-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.1));
  border-radius: 50%;
  opacity: 0.9;
}

.send-btn::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, transparent, rgba(0, 216, 224, 0.5), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.send-btn:hover::after {
  opacity: 1;
  animation: rotate 2s linear infinite;
}

.send-btn i {
  position: relative;
  z-index: 2;
  font-size: 1.1rem;
}

@keyframes pulse {
  0% {
    box-shadow: 0 8px 24px rgba(0, 216, 224, 0.5), 0 0 0 0 rgba(0, 216, 224, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 8px 24px rgba(0, 216, 224, 0.5), 0 0 0 15px rgba(0, 216, 224, 0);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 8px 24px rgba(0, 216, 224, 0.5), 0 0 0 0 rgba(0, 216, 224, 0);
    transform: scale(1);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes send-button-hover {
  0% {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 24px rgba(0, 216, 224, 0.5), 0 0 15px rgba(0, 216, 224, 0.3);
  }
  50% {
    transform: translateY(-4px) scale(1.08);
    box-shadow: 0 10px 28px rgba(0, 216, 224, 0.6), 0 0 20px rgba(0, 216, 224, 0.4);
  }
  100% {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 24px rgba(0, 216, 224, 0.5), 0 0 15px rgba(0, 216, 224, 0.3);
  }
}

/* Stop button styling */
.send-btn.stop-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  box-shadow: 0 6px 18px rgba(231, 76, 60, 0.35);
}

.send-btn.stop-btn:hover {
  background: linear-gradient(135deg, #c0392b, #e74c3c);
  box-shadow: 0 8px 24px rgba(231, 76, 60, 0.5), 0 0 15px rgba(231, 76, 60, 0.3);
}

.input-icon-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 166, 192, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 166, 192, 0.2);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.85rem;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  flex-shrink: 0; /* Prevent button from shrinking */
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.input-icon-btn:hover {
  background-color: rgba(0, 166, 192, 0.2);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 166, 192, 0.2);
}

.input-icon-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.file-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(20, 20, 20, 0.8);
  color: var(--text-color);
  border: var(--glass-border);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.85rem;
  box-shadow: var(--shadow);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  flex-shrink: 0; /* Prevent button from shrinking */
}

.file-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  border-radius: 50%;
  opacity: 0.5;
}

.file-btn i {
  position: relative;
  z-index: 2;
}

/* File Source Popup Styles */
.file-source-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.file-source-popup-content {
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  width: 360px;
  overflow: hidden;
  box-shadow: var(--shadow);
  border: var(--glass-border);
  animation: fadeIn 0.3s ease-out;
}

.file-source-header {
  padding: 16px;
  border-bottom: var(--glass-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--primary-gradient);
}

.file-source-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--white);
  font-weight: 500;
}

.close-popup-btn {
  background: transparent;
  border: none;
  color: var(--white);
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.close-popup-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.file-source-options {
  display: flex;
  padding: 20px;
  gap: 15px;
}

.file-source-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  text-align: center;
}

.file-source-option:hover {
  background-color: rgba(0, 136, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.file-source-icon {
  font-size: 24px;
  margin-bottom: 10px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  box-shadow: var(--shadow);
}

.file-source-label {
  font-size: 14px;
  color: var(--text-color);
}

/* Creative Studio Dialog Styles */
.modal-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 380px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-hover);
  border: var(--glass-border);
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: var(--glass-border);
  background: var(--primary-gradient);
}

.modal-header h3 {
  font-size: 1.2rem;
  color: var(--white);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  font-family: var(--font-primary);
  letter-spacing: 0.5px;
}

.close-btn {
  background: transparent;
  border: none;
  color: var(--white);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  width: 30px;
  height: 30px;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 20px;
}

/* Creative Tools Grid Styles */
.creative-tools-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 10px;
}

.creative-tool-card {
  background-color: rgba(45, 45, 45, 0.5);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  border: var(--glass-border);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  box-shadow: var(--shadow);
}

.creative-tool-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-hover);
  background-color: rgba(0, 136, 255, 0.15);
  border-color: rgba(0, 136, 255, 0.3);
}

.creative-tool-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  font-size: 20px;
  color: white;
  box-shadow: var(--shadow);
}

.creative-tool-card h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  font-family: var(--font-primary);
  letter-spacing: 0.3px;
}

.creative-tool-card p {
  margin: 0;
  font-size: 13px;
  color: var(--text-light);
  line-height: 1.4;
  font-family: var(--font-secondary);
}

@keyframes slideUp {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

.file-upload-input {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}

.file-preview {
  padding: 10px 12px;
  background-color: rgba(0, 136, 255, 0.1);
  border-top: var(--glass-border);
  font-size: 14px;
  margin-top: 5px;
}

.file-preview-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

#fileSource {
  display: flex;
  align-items: center;
  margin-right: 8px;
  color: var(--primary-color);
}

#fileSource i {
  font-size: 14px;
}

#fileName {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--text-color);
  font-family: var(--font-secondary);
}

.remove-file-btn {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  padding: 4px;
  margin-left: 8px;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-file-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--danger-color);
}

.message.file-info {
  font-style: italic;
  padding: 4px 8px;
  background-color: rgba(0, 180, 216, 0.05);
  border-radius: 4px;
  margin-bottom: 4px;
}

/* Settings styles */
  font-size: 0.95rem;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  box-shadow: var(--shadow-inset);
  letter-spacing: 0.5px;
  font-family: var(--font-secondary);
}

.dashboard-input-wrapper input:focus {
  outline: none;
  border-color: var(--primary-color);
  background-color: rgba(61, 61, 61, 0.9);
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
}

.input-highlight {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  width: 0;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  transition: width 0.3s ease;
  z-index: 2;
}

.dashboard-input-wrapper input:focus ~ .input-highlight {
  width: 100%;
  animation: pulse 2s infinite;
}

.connect-btn {
  padding: 0 20px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 120px;
  box-shadow: var(--shadow);
}

.connect-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.4);
  background: linear-gradient(135deg, var(--primary-light), var(--secondary-light));
}

.connect-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(67, 97, 238, 0.3);
}

.connection-status {
  margin: 15px 0;
  padding: 15px;
  border-radius: 10px;
  background-color: rgba(30, 30, 46, 0.7);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  box-shadow: var(--shadow);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
}

.status-text {
  font-weight: 600;
  font-size: 0.95rem;
}

.status-indicator.connected {
  color: #2ecc71;
}

.status-indicator.connected .status-icon {
  animation: pulse 2s infinite;
}

.status-indicator.disconnected {
  color: #e74c3c;
}

.status-indicator.connecting {
  color: #f39c12;
}

.status-indicator.connecting .status-icon {
  animation: spin 1.5s linear infinite;
}

.status-message {
  font-size: 0.85rem;
  color: var(--text-light);
  margin: 0;
  line-height: 1.5;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.action-btn {
  padding: 8px 12px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background-color: var(--primary-light);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Destress Mode Styles */
.destress-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.destress-content {
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 400px;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-hover);
  border: var(--glass-border);
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
}

.destress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: var(--glass-border);
  background: var(--primary-gradient);
}

.destress-header h3 {
  font-size: 1.2rem;
  color: var(--white);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.destress-close-btn {
  background: transparent;
  border: none;
  color: var(--white);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.destress-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.destress-body {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-light) var(--bg-light);
}

.destress-body::-webkit-scrollbar {
  width: 6px;
}

.destress-body::-webkit-scrollbar-track {
  background: var(--bg-light);
}

.destress-body::-webkit-scrollbar-thumb {
  background-color: var(--primary-light);
  border-radius: 20px;
}

/* Destress Tabs */
.destress-tabs {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 12px;
  margin-top: -5px;
}

.destress-tab {
  background: transparent;
  border: none;
  color: var(--text-color);
  padding: 8px 12px;
  cursor: pointer;
  font-size: 0.85rem;
  border-bottom: 2px solid transparent;
  transition: var(--transition);
  flex: 1;
  text-align: center;
}

.destress-tab.active {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  font-weight: 500;
}

.destress-tab:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.destress-tab-content {
  display: none;
}

.destress-tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease-out;
}

/* Recommendations */
.recommendations-title {
  font-size: 1rem;
  margin-bottom: 3px;
  color: var(--text-color);
}

.recommendations-subtitle {
  font-size: 0.75rem;
  color: var(--text-light);
  margin-bottom: 12px;
}

.recommendations-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.recommendation-card {
  display: flex;
  background-color: rgba(45, 45, 45, 0.5);
  border-radius: var(--border-radius-sm);
  padding: 10px;
  cursor: pointer;
  transition: var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.05);
  margin-bottom: 10px;
}

.recommendation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-color: var(--primary-color);
  background-color: rgba(0, 136, 255, 0.1);
}

.recommendation-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: var(--primary-dark);
  border-radius: 50%;
  margin-right: 10px;
  flex-shrink: 0;
}

.recommendation-icon i {
  font-size: 0.9rem;
  color: var(--white);
}

.recommendation-content {
  flex: 1;
  overflow: hidden;
}

.recommendation-content h5 {
  font-size: 0.9rem;
  margin: 0 0 3px 0;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recommendation-content p {
  font-size: 0.75rem;
  color: var(--text-light);
  margin: 0 0 5px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 2.6em;
}

.recommendation-platform {
  font-size: 0.7rem;
  color: var(--primary-color);
  display: inline-block;
  padding: 2px 6px;
  background-color: rgba(0, 136, 255, 0.1);
  border-radius: 10px;
}

.technique-card {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-sm);
  padding: 20px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.technique-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.technique-card h4 {
  font-size: 1.2rem;
  margin-bottom: 5px;
  color: var(--white);
}

.technique-duration {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 15px;
  font-style: italic;
}

.technique-description {
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-color);
}

.destress-actions {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.next-technique-btn {
  background-color: var(--surface-color);
  color: var(--text-color);
  border: 1px solid var(--primary-color);
  border-radius: var(--border-radius-sm);
  padding: 10px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
}

.next-technique-btn:hover {
  background-color: var(--primary-dark);
  color: var(--white);
}

/* Destress button styles */
.destress-btn {
  background-color: var(--input-bg);
  color: var(--text-color);
  border: var(--glass-border);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  margin-left: 0;
  margin-right: 8px;
  box-shadow: var(--shadow);
  font-size: 1rem;
}

.destress-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--input-glow);
  background-color: rgba(0, 136, 255, 0.15);
  color: var(--primary-color);
}

/* Persistent Toggle Menu has been removed */

/* Pulse animation has been removed */

/* Special styles for blank page sidebars */
body.blank-page-sidebar {
  /* Ensure all elements are visible and interactive */
  z-index: 9999999 !important;
  pointer-events: auto !important;
}

body.blank-page-sidebar * {
  /* Ensure all child elements are interactive */
  pointer-events: auto !important;
}

body.blank-page-sidebar #userInput,
body.blank-page-sidebar #sendMessage,
body.blank-page-sidebar .tab-btn,
body.blank-page-sidebar button,
body.blank-page-sidebar select,
body.blank-page-sidebar a {
  /* Ensure interactive elements work */
  pointer-events: auto !important;
  cursor: pointer !important;
}

body.blank-page-sidebar #userInput {
  /* Ensure input field is properly styled */
  background-color: var(--input-bg) !important;
  color: var(--text-color) !important;
  border: 1px solid var(--border-color) !important;
}

body.blank-page-sidebar #sendMessage {
  /* Ensure send button is properly styled */
  background-color: var(--accent-color) !important;
  color: white !important;
  opacity: 1 !important;
}

/* Force visibility of elements in blank page sidebar */
body.blank-page-sidebar .tab-content.active {
  display: flex !important;
}

body.blank-page-sidebar .tab-btn.active {
  color: var(--white) !important;
  background: var(--primary-gradient) !important;
}

/* Ensure proper z-index for elements in blank page sidebar */
body.blank-page-sidebar .container,
body.blank-page-sidebar main,
body.blank-page-sidebar header,
body.blank-page-sidebar footer {
  z-index: auto !important;
}

/* Special styles for direct loading */
body.direct-load {
  /* Ensure all elements are visible and interactive */
  z-index: 9999999 !important;
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

body.direct-load * {
  /* Ensure all child elements are interactive */
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Special styles for new tab pages */
body.new-tab-sidebar {
  /* Ensure all elements are visible and interactive */
  z-index: 9999999 !important;
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

body.new-tab-sidebar * {
  /* Ensure all child elements are interactive */
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure buttons are properly styled and interactive on new tab pages */
body.new-tab-sidebar button,
body.new-tab-sidebar .tab-btn,
body.new-tab-sidebar #sendMessage,
body.new-tab-sidebar #userInput,
body.new-tab-sidebar select,
body.new-tab-sidebar a {
  cursor: pointer !important;
  pointer-events: auto !important;
  z-index: 99999 !important;
  position: relative !important;
}

/* Force visibility of elements in direct load mode */
body.direct-load .tab-content.active {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

body.direct-load #userInput,
body.direct-load #sendMessage,
body.direct-load .tab-btn,
body.direct-load button,
body.direct-load select,
body.direct-load a {
  /* Ensure interactive elements work */
  pointer-events: auto !important;
  cursor: pointer !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* AI Agent Browser Styles */
.ai-agent-progress {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 16px;
  color: white;
  margin: 8px 0;
}

.progress-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.progress-header i {
  font-size: 18px;
  color: #00d4ff;
}

.progress-title {
  font-weight: 600;
  font-size: 14px;
}

.progress-stage {
  font-size: 13px;
  margin-bottom: 8px;
  opacity: 0.9;
}

.progress-bar {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  height: 6px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  background: linear-gradient(90deg, #00d4ff, #00a6c0);
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
  width: 0%;
}

.progress-details {
  font-size: 11px;
  opacity: 0.8;
}

/* AI Agent Response Styles */
.ai-agent-response {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 20px;
  margin: 12px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  color: #333;
}

.ai-agent-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #00a6c0;
}

.ai-agent-header i {
  font-size: 20px;
  color: #00a6c0;
}

.ai-agent-title {
  font-weight: 700;
  font-size: 16px;
  color: #283b48;
}

.confidence-badge {
  background: linear-gradient(135deg, #00a6c0, #48d7ce);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  margin-left: auto;
}

.ai-agent-answer {
  margin-bottom: 20px;
}

.ai-agent-answer h4 {
  color: #283b48;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.ai-agent-answer h4::before {
  content: "💡";
  font-size: 16px;
}

.ai-agent-answer p {
  line-height: 1.6;
  color: #495057;
  margin: 0;
}

.ai-agent-findings {
  margin-bottom: 20px;
}

.ai-agent-findings h4 {
  color: #283b48;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.ai-agent-findings h4::before {
  content: "🔍";
  font-size: 16px;
}

.ai-agent-findings ul {
  margin: 0;
  padding-left: 20px;
}

.ai-agent-findings li {
  margin-bottom: 6px;
  line-height: 1.5;
  color: #495057;
}

.ai-agent-sources {
  margin-bottom: 20px;
}

.ai-agent-sources h4 {
  color: #283b48;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.ai-agent-sources h4::before {
  content: "📚";
  font-size: 16px;
}

.sources-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.source-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
}

.source-item:hover {
  border-color: #00a6c0;
  box-shadow: 0 2px 8px rgba(0, 166, 192, 0.1);
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
  gap: 10px;
}

.source-title {
  color: #00a6c0;
  text-decoration: none;
  font-weight: 600;
  font-size: 13px;
  line-height: 1.4;
  flex: 1;
}

.source-title:hover {
  text-decoration: underline;
}

.source-reliability {
  background: #e9ecef;
  color: #495057;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  white-space: nowrap;
}

.source-hostname {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 6px;
}

.source-snippet {
  font-size: 12px;
  color: #495057;
  line-height: 1.4;
}

.ai-agent-followup {
  margin-bottom: 20px;
}

.ai-agent-followup h4 {
  color: #283b48;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.ai-agent-followup h4::before {
  content: "❓";
  font-size: 16px;
}

.followup-questions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.followup-question {
  background: linear-gradient(135deg, #00a6c0, #48d7ce);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.followup-question:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 166, 192, 0.3);
}

.ai-agent-limitations {
  margin-bottom: 20px;
  padding: 12px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
}

.ai-agent-limitations h4 {
  color: #856404;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.ai-agent-limitations h4::before {
  content: "⚠️";
  font-size: 16px;
}

.ai-agent-limitations p {
  color: #856404;
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}

.ai-agent-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #dee2e6;
}

.search-time {
  font-size: 11px;
  color: #6c757d;
}

.view-details-btn {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.view-details-btn:hover {
  background: #495057;
  transform: translateY(-1px);
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  color: #721c24;
  margin: 8px 0;
}

.error-message i {
  color: #dc3545;
}

/* Search Details Popup */
.search-details-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(5px);
}

.search-details-content {
  background: #2a2a2a;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.search-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #00a6c0, #48d7ce);
  color: white;
}

.search-details-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-details-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.close-details-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.search-details-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
  color: #e0e0e0;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  color: #00a6c0;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.detail-section pre {
  background: #1a1a1a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 12px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  color: #e0e0e0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

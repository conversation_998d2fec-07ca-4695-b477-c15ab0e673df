<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- Disable all client hints features -->
  <meta http-equiv="Permissions-Policy" content="interest-cohort=(), ch-dpr=(), ch-viewport-width=(), ch-viewport-height=(), ch-width=(), ch-height=(), ch-device-memory=(), ch-ua=(), ch-ua-arch=(), ch-ua-platform=(), ch-ua-model=(), ch-ua-mobile=(), ch-ua-full-version=(), ch-ua-platform-version=(), ch-prefers-color-scheme=(), ch-downlink=(), ch-ect=(), ch-rtt=(), ch-save-data=(), ch-ua-bitness=(), ch-ua-wow64=(), clipboard-read=(), clipboard-write=()">
  <!-- Add strict Content Security Policy -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: blob:; connect-src 'self' https://*.openai.com https://openrouter.ai https://*.openrouter.ai https://*.googleapis.com https://*.google.com https://*.anthropic.com https://*.mistral.ai https://*.deepseek.com https://*.cohere.com http://localhost:5000 http://localhost:5173; frame-src 'self'">
  <!-- Script to disable client hints (external file) -->
  <script src="../scripts/client-hints-disabler.js"></script>
  <title>Browzy AI</title>
  <link rel="stylesheet" href="popup.css">
  <link rel="stylesheet" href="../scripts/productivity-links.css">
  <link rel="stylesheet" href="webpage-access.css">
  <link rel="stylesheet" href="prompt-styles.css">
  <link rel="stylesheet" href="selection-highlight.css">
  <link rel="stylesheet" href="editable-messages.css">
  <link rel="stylesheet" href="modern-ui.css">
  <link rel="stylesheet" href="sidebar-mode.css">
  <link rel="stylesheet" href="modern-nav.css">
  <link rel="stylesheet" href="settings-ui.css">
  <link rel="stylesheet" href="history-ui.css">
  <link rel="stylesheet" href="stats-enhanced.css">
  <link rel="stylesheet" href="creative-studio.css">
  <link rel="stylesheet" href="stop-button.css">
  <link rel="stylesheet" href="shortcuts.css">
  <link rel="stylesheet" href="voice-command.css">
  <link rel="stylesheet" href="web-mcp.css">
  <link rel="stylesheet" href="saved-links.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700&family=Nunito+Sans:wght@400;600;700&family=Roboto+Mono:wght@400;500&display=swap">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;500;600&display=swap">
  <!-- Berkeley Mono is a premium font, so we'll add a fallback system -->
  <style>
    @font-face {
      font-family: 'Berkeley Mono';
      src: local('Berkeley Mono');
      font-weight: normal;
      font-style: normal;
    }
    @font-face {
      font-family: 'Berkeley Mono';
      src: local('Berkeley Mono Bold');
      font-weight: bold;
      font-style: normal;
    }
  </style>
  <style>
    html, body {
      background-color: var(--bg-color);
      border-radius: var(--border-radius);
      overflow: hidden;
    }
  </style>
</head>
<body>
  <!-- Enhanced Animation Overlay -->
  <div id="animationOverlay" class="animation-overlay">
    <div class="animation-content">
      <div class="animation-background">
        <div class="particle particle-1"></div>
        <div class="particle particle-2"></div>
        <div class="particle particle-3"></div>
        <div class="particle particle-4"></div>
        <div class="particle particle-5"></div>
        <div class="particle particle-6"></div>
        <div class="particle particle-7"></div>
        <div class="particle particle-8"></div>
        <div class="glow-effect"></div>
        <div class="pulse-ring"></div>
      </div>
      <div class="logo-animation">
        <img src="../images/icon.png" alt="Browzy AI" class="animated-logo">
      </div>
      <div class="brand-animation">
        <div class="brand-text">
          <span class="letter">B</span>
          <span class="letter">r</span>
          <span class="letter">o</span>
          <span class="letter">w</span>
          <span class="letter">z</span>
          <span class="letter">y</span>
          <span class="letter space"> </span>
          <span class="letter">A</span>
          <span class="letter">I</span>
        </div>
      </div>
      <div class="welcome-message">
        <p class="welcome-text">Browzy is ready to assist you</p>
      </div>
      <div class="loading-indicator-animation">
        <span class="dot dot-1"></span>
        <span class="dot dot-2"></span>
        <span class="dot dot-3"></span>
      </div>
    </div>
  </div>

  <!-- Close button removed as requested -->
  <div class="container">
    <header>
      <h1><img src="../images/icon.png" alt="Browzy AI Logo" class="logo-img"> Browzy AI</h1>
      <div class="tabs">
        <button id="chatTab" class="tab-btn active"><i class="fas fa-comment-alt"></i><span>Chat</span></button>
        <button id="historyTab" class="tab-btn"><i class="fas fa-history"></i><span>History</span></button>
        <button id="settingsTab" class="tab-btn"><i class="fas fa-cog"></i><span>Settings</span></button>
        <button id="statsTab" class="tab-btn"><i class="fas fa-chart-bar"></i><span>Stats</span></button>
        <button id="sidebarToggleBtn" class="sidebar-toggle-btn" title="Toggle Sidebar Mode"><i class="fas fa-columns"></i></button>
      </div>
      <!-- No additional close buttons needed, using the super close button instead -->
    </header>

    <main>
      <!-- Chat Tab Content -->
      <section id="chatContent" class="tab-content active">
        <div class="provider-selector">
          <div style="display: flex; width: 100%; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 8px;">
            <select id="providerSelector">


              <!-- Direct OpenAI Models (OpenAI API Key Required) -->
              <optgroup label="OpenAI Models (OpenAI API Key)">
                <option value="direct-openai/gpt-4o">GPT-4o</option>
                <option value="direct-openai/gpt-4o-mini">GPT-4o Mini</option>
                <option value="direct-openai/gpt-4-turbo">GPT-4 Turbo</option>
                <option value="direct-openai/gpt-4">GPT-4</option>
                <option value="direct-openai/gpt-3.5-turbo">GPT-3.5 Turbo</option>
                <option value="direct-openai/gpt-3.5-turbo-16k">GPT-3.5 Turbo 16K</option>
              </optgroup>

              <!-- Direct Gemini Models (Gemini API Key Required) -->
              <optgroup label="Gemini Models (Gemini API Key)">
                <option value="direct-gemini/gemini-1.5-flash" selected>Gemini 1.5 Flash</option>
                <option value="direct-gemini/gemini-1.5-flash-latest">Gemini 1.5 Flash Latest</option>
                <option value="direct-gemini/gemini-1.5-flash-8b">Gemini 1.5 Flash 8B</option>
                <option value="direct-gemini/gemini-2.0-flash-exp">Gemini 2.0 Flash (Experimental)</option>
              </optgroup>

              <!-- Hugging Face Models have been removed as requested -->

              <!-- OpenRouter Models (OpenRouter API Key Required) -->
              <optgroup label="OpenRouter Models (OpenRouter API Key)">
                <!-- OpenAI Models -->
                <option value="openai/gpt-4o">GPT-4o (OpenAI)</option>
                <option value="openai/gpt-4o-mini">GPT-4o Mini (OpenAI)</option>
                <option value="openai/gpt-4-turbo">GPT-4 Turbo (OpenAI)</option>
                <option value="openai/gpt-4">GPT-4 (OpenAI)</option>
                <option value="openai/gpt-3.5-turbo">GPT-3.5 Turbo (OpenAI)</option>

                <!-- Anthropic Models -->
                <option value="anthropic/claude-3-opus">Claude 3 Opus (Anthropic)</option>
                <option value="anthropic/claude-3-sonnet">Claude 3 Sonnet (Anthropic)</option>
                <option value="anthropic/claude-3-haiku">Claude 3 Haiku (Anthropic)</option>
                <option value="anthropic/claude-2">Claude 2 (Anthropic)</option>

                <!-- Mistral Models -->
                <option value="mistralai/mistral-medium-3">Mistral Medium 3 (Mistral)</option>
                <option value="mistralai/mistral-7b-instruct">Mistral 7B Instruct (Mistral)</option>

                <!-- Specialized Models -->
                <option value="deepseek/deepseek-coder-v2">DeepSeek Coder V2 (Code)</option>
                <option value="cohere/command-r-plus">Command R+ (Cohere)</option>
                <option value="meta-llama/llama-3-70b-instruct">Llama 3 70B (Meta)</option>
                <option value="meta-llama/llama-3-8b-instruct">Llama 3 8B (Meta)</option>
              </optgroup>
            </select>

            <div class="actions-dropdown-container">
              <span id="actionsDropdownBtn" class="action-icon" title="Actions"><i class="fas fa-ellipsis-v"></i></span>
              <span id="toggleFloatingChat" class="action-icon" title="AI Agent Browser"><i class="fas fa-robot"></i></span>
              <span id="sidebarToggleBtn2" class="action-icon" title="Open in Sidebar"><i class="fas fa-columns"></i></span>
              <span id="clearChat" class="action-icon" title="Clear chat"><i class="fas fa-trash-alt"></i></span>
              <div id="actionsDropdownMenu" class="actions-dropdown-menu">
                <div class="dropdown-item" id="translateButton" data-shortcut="⌘+Shift+M">
                  <i class="fas fa-language"></i>
                  <span>Translate text</span>
                </div>
                <div class="dropdown-item" id="scanTabButton" data-shortcut="⌘+J">
                  <i class="fas fa-search"></i>
                  <span>Scan another tab</span>
                </div>

                <div class="dropdown-item" id="exportChatButton" data-shortcut="⌘+D">
                  <i class="fas fa-sticky-note"></i>
                  <span>Export Chat</span>
                </div>
                <div class="dropdown-divider"></div>
                <div class="dropdown-item" id="webMcpButton">
                  <i class="fas fa-globe-americas"></i>
                  <span>Web MCP</span>
                </div>
                <div class="dropdown-item" id="websiteAnalyzerButton" data-shortcut="Del">
                  <i class="fas fa-chart-line"></i>
                  <span>Website Analyzer</span>
                </div>
                <div class="dropdown-divider"></div>
                <div class="dropdown-item" id="creativeStudioButton">
                  <i class="fas fa-paint-brush"></i>
                  <span>Creative Studio</span>
                </div>
                <div class="dropdown-item" id="pdfToolsButton">
                  <i class="fas fa-file-pdf"></i>
                  <span>PDF Tools</span>
                </div>
                <div class="dropdown-item" id="videoAnalysisButton">
                  <i class="fas fa-video"></i>
                  <span>Video Analysis</span>
                </div>
                <div class="dropdown-divider"></div>
                <div class="dropdown-item" id="savedLinksButton">
                  <i class="fas fa-link"></i>
                  <span>Saved Links</span>
                </div>
              </div>
            </div>
          </div>

        </div>

        <div id="chatMessages" class="chat-messages">
          <div class="message ai-message">
            <div class="message-avatar">
              <img src="../images/icon.png" alt="Browzy AI" class="infinity-logo">
            </div>
            <div class="message-content">
              <p>👋 Hello! I'm Browzy AI, your intelligent assistant. I can analyze any webpage you're viewing and help with any request.</p>
            </div>
          </div>
        </div>

        <div class="chat-input-container">
          <textarea id="userInput" placeholder="Ask me anything..." rows="1"></textarea>
          <div class="chat-input-buttons">
            <input type="file" id="fileUpload" class="file-upload-input" accept=".pdf,.txt,.csv,.docx,.doc,.xlsx,.xls,.json,.md,.html,.xml">
            <button id="fileUploadBtn" class="input-icon-btn" title="Upload from computer"><i class="fas fa-plus"></i></button>
            <button id="destressBtn" class="input-icon-btn" title="Destress mode"><i class="fas fa-spa"></i></button>
            <button id="sendMessage" class="send-btn" title="Send message"><i class="fas fa-paper-plane"></i></button>
          </div>
        </div>

        <!-- Stop Button for interrupting AI responses -->
        <div class="stop-button-container" id="stopButtonContainer">
          <button id="stopResponseBtn" class="stop-button pulse" title="Stop AI response">
            <i class="fas fa-stop"></i>
          </button>
        </div>

        <!-- Typing indicator for AI responses -->
        <div class="typing-indicator" id="typingIndicator" style="display: none;">
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
        </div>

        <!-- Modern Floating Chat UI -->
        <div class="floating-chat-container" style="display: none;">
          <div class="floating-chat-header">
            <div class="floating-chat-title">
              <img src="../images/icon.png" alt="Browzy AI">
              <div>
                <div>AI Agent Browser</div>
                <div class="floating-chat-subtitle">Intelligent web browsing with AI-powered search & analysis</div>
              </div>
            </div>
            <button class="floating-chat-close">Stop</button>
          </div>

          <div class="floating-chat-messages">
            <!-- Messages will be added here -->
          </div>

          <div class="floating-chat-input-container">
            <textarea class="floating-chat-input" placeholder="Ask me anything... I'll search, analyze, and provide comprehensive answers with sources"></textarea>
          </div>

          <div class="floating-chat-controls">
            <button class="floating-chat-control-button floating-actions-dropdown-btn" title="Menu"><i class="fas fa-ellipsis-v"></i></button>
            <div class="floating-actions-dropdown-menu">
              <div class="dropdown-item" id="floatingTranslateButton" data-shortcut="⌘+Shift+M">
                <i class="fas fa-language"></i>
                <span>Translate text</span>
              </div>
              <div class="dropdown-item" id="floatingScanTabButton" data-shortcut="⌘+J">
                <i class="fas fa-search"></i>
                <span>Scan another tab</span>
              </div>

              <div class="dropdown-item" id="floatingExportChatButton" data-shortcut="⌘+D">
                <i class="fas fa-sticky-note"></i>
                <span>Export Chat</span>
              </div>
              <div class="dropdown-divider"></div>
              <div class="dropdown-item" id="floatingWebMcpButton">
                <i class="fas fa-globe-americas"></i>
                <span>Web MCP</span>
              </div>
              <div class="dropdown-item" id="floatingWebsiteAnalyzerButton" data-shortcut="Del">
                <i class="fas fa-chart-line"></i>
                <span>Website Analyzer</span>
              </div>
              <div class="dropdown-divider"></div>
              <div class="dropdown-item" id="floatingCreativeStudioButton">
                <i class="fas fa-paint-brush"></i>
                <span>Creative Studio</span>
              </div>
              <div class="dropdown-item" id="floatingPdfToolsButton">
                <i class="fas fa-file-pdf"></i>
                <span>PDF Tools</span>
              </div>
              <div class="dropdown-item" id="floatingVideoAnalysisButton">
                <i class="fas fa-video"></i>
                <span>Video Analysis</span>
              </div>
            </div>
          </div>

          <button class="floating-chat-up-button"><i class="fas fa-chevron-up"></i></button>
        </div>

        <!-- File Source Selection Popup -->
        <div id="fileSourcePopup" class="file-source-popup">
          <div class="file-source-popup-content">
            <div class="file-source-header">
              <h3>Files & Tools</h3>
              <button id="closeFileSourcePopup" class="close-popup-btn"><i class="fas fa-times"></i></button>
            </div>
            <div class="file-source-options">
              <div class="file-source-option" id="localFileOption">
                <div class="file-source-icon">
                  <i class="fas fa-desktop"></i>
                </div>
                <div class="file-source-label">From Computer</div>
              </div>

              <div class="file-source-option" id="destressOption">
                <div class="file-source-icon">
                  <i class="fas fa-peace"></i>
                </div>
                <div class="file-source-label">Destress Mode</div>
              </div>
            </div>
          </div>
        </div>

        <div id="filePreview" class="file-preview" style="display: none;">
          <div class="file-preview-content">
            <span id="fileSource"><i class="fas fa-desktop"></i></span>
            <span id="fileName"></span>
            <button id="removeFile" class="remove-file-btn" title="Remove file"><i class="fas fa-times"></i></button>
          </div>
        </div>



        <!-- Suggestion chips removed to provide more space for chat -->
        <div class="suggestion-chips" style="display: none;">
        </div>
      </section>

      <!-- Settings Tab Content -->
      <section id="settingsContent" class="tab-content">
        <div class="settings-header">
          <h2><i class="fas fa-key"></i> API Key Management</h2>
          <p class="settings-description">Configure your API keys to access different AI models</p>
        </div>

        <div class="api-key-form">
          <div class="api-key-cards">


            <div class="api-key-card">
              <div class="api-key-card-header">
                <div class="api-key-icon openrouter"><i class="fas fa-network-wired"></i></div>
                <div class="api-key-title">OpenRouter</div>
                <div class="api-key-status" id="openrouterStatus"><i class="fas fa-circle"></i> Not Set</div>
              </div>
              <div class="api-key-input">
                <input type="password" id="openrouterKey" placeholder="Enter OpenRouter API Key">
                <button class="toggle-visibility" title="Show/Hide API Key"><i class="fas fa-eye"></i></button>
              </div>
              <div class="api-key-description">
                Access to Claude, GPT-4, and other premium models through a single API.
                <a href="https://openrouter.ai/keys" target="_blank" class="api-key-link">Get API Key <i class="fas fa-external-link-alt"></i></a>
              </div>
            </div>

            <div class="api-key-card">
              <div class="api-key-card-header">
                <div class="api-key-icon openai"><i class="fas fa-robot"></i></div>
                <div class="api-key-title">OpenAI</div>
                <div class="api-key-status" id="openaiStatus"><i class="fas fa-circle"></i> Not Set</div>
              </div>
              <div class="api-key-input">
                <input type="password" id="openaiKey" placeholder="Enter OpenAI API Key">
                <button class="toggle-visibility" title="Show/Hide API Key"><i class="fas fa-eye"></i></button>
              </div>
              <div class="api-key-description">
                Direct access to GPT-4o, GPT-4 Turbo, GPT-3.5 Turbo, and more.
                <a href="https://platform.openai.com/api-keys" target="_blank" class="api-key-link">Get API Key <i class="fas fa-external-link-alt"></i></a>
              </div>
            </div>

            <div class="api-key-card">
              <div class="api-key-card-header">
                <div class="api-key-icon gemini"><i class="fas fa-brain"></i></div>
                <div class="api-key-title">Gemini</div>
                <div class="api-key-status" id="geminiStatus"><i class="fas fa-circle"></i> Not Set</div>
              </div>
              <div class="api-key-input">
                <input type="password" id="geminiKey" placeholder="Enter Gemini API Key">
                <button class="toggle-visibility" title="Show/Hide API Key"><i class="fas fa-eye"></i></button>
              </div>
              <div class="api-key-description">
                Access to Google's Gemini 1.5 Pro, Flash, and Vision models.
                <a href="https://aistudio.google.com/app/apikey" target="_blank" class="api-key-link">Get API Key <i class="fas fa-external-link-alt"></i></a>
              </div>
            </div>

            <!-- Hugging Face API key card has been removed as requested -->
          </div>

          <div class="api-key-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <span>All models require an API key to function properly</span>
          </div>

          <button id="saveKeys" class="save-btn"><i class="fas fa-save"></i> Save API Keys</button>
        </div>

        <div class="settings-header">
          <h2><i class="fas fa-link"></i> Dashboard Connection</h2>
          <p class="settings-description">Connect to the Browzy AI dashboard to sync your chat history and settings</p>
        </div>

        <!-- User Profile Section -->
        <div id="userProfileSection" class="user-profile-section" style="display: block !important;">
          <div class="profile-card">
            <div class="profile-header">
              <div class="profile-avatar">
                <i class="fas fa-user-circle"></i>
              </div>
              <div class="profile-info">
                <div class="profile-name" id="profileUsername">Not Connected</div>
                <div class="profile-plan" id="profilePlan">Connect to see your plan</div>
              </div>
            </div>
            <div class="profile-details">
              <div class="profile-detail">
                <i class="fas fa-crown"></i>
                <span id="profilePlanFeatures">No premium features available</span>
              </div>
              <div class="profile-detail">
                <i class="fas fa-chart-line"></i>
                <span id="profileUsageLimit">Usage limit: N/A</span>
              </div>
            </div>
          </div>
        </div>

        <div class="settings-form">
          <div class="dashboard-card">
            <div class="dashboard-input-container">
              <label for="dashboardToken"><i class="fas fa-key"></i> Connection Token</label>
              <div class="dashboard-connection-container">
                <div class="dashboard-input-wrapper">
                  <input type="text" id="dashboardToken" placeholder="Enter 9-character token from dashboard">
                  <div class="input-highlight"></div>
                </div>
                <button id="connectDashboard" class="connect-btn" title="Connect to Dashboard"><i class="fas fa-plug"></i> Connect</button>
              </div>
              <div style="margin-top: 8px; display: flex; justify-content: flex-end;">
                <button id="testConnection" class="secondary-btn">
                  <i class="fas fa-network-wired"></i> Test Connection
                </button>
              </div>
            </div>

            <div id="dashboardConnectionStatus" class="connection-status">
              <div class="status-indicator disconnected">
                <div class="status-icon"><i class="fas fa-times-circle"></i></div>
                <div class="status-text">Not connected to dashboard</div>
              </div>
              <p class="status-message">Connect to the BrowzyAI dashboard to sync your chat history and settings.</p>
            </div>

            <div class="dashboard-steps">
              <div class="dashboard-step">
                <div class="step-number">1</div>
                <div class="step-content">Log in to the <a href="http://localhost:5173/dashboard" target="_blank">BrowzyAI Dashboard</a></div>
              </div>
              <div class="dashboard-step">
                <div class="step-number">2</div>
                <div class="step-content">Copy the connection token from the dashboard</div>
              </div>
              <div class="dashboard-step">
                <div class="step-number">3</div>
                <div class="step-content">Paste the token here and click Connect</div>
              </div>
            </div>
          </div>
        </div>

        <div class="settings-header">
          <h2><i class="fas fa-keyboard"></i> Keyboard Shortcuts</h2>
          <p class="settings-description">Use keyboard shortcuts to navigate and control Browzy AI more efficiently</p>
        </div>

        <div class="shortcuts-container">
          <div class="shortcuts-grid">
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>1</kbd></div>
              <div class="shortcut-description">Switch to Chat tab</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>2</kbd></div>
              <div class="shortcut-description">Switch to History tab</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>3</kbd></div>
              <div class="shortcut-description">Switch to Stats tab</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>4</kbd></div>
              <div class="shortcut-description">Switch to Settings tab</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>/</kbd></div>
              <div class="shortcut-description">Focus on chat input</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>C</kbd></div>
              <div class="shortcut-description">Clear chat</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Esc</kbd></div>
              <div class="shortcut-description">Stop AI generation</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Enter</kbd></div>
              <div class="shortcut-description">Send message</div>
            </div>
          </div>
        </div>

        <div class="settings-header">
          <h2><i class="fas fa-sliders-h"></i> Chat Settings</h2>
          <p class="settings-description">Customize your chat experience</p>
        </div>

        <div class="settings-form">
          <div class="settings-grid">
            <div class="setting-card">
              <div class="setting-card-header">
                <i class="fas fa-text-width"></i>
                <span>Response Length</span>
              </div>
              <div class="setting-card-content">
                <input type="number" id="maxTokens" min="100" max="16000" value="1000">
                <div class="setting-hint">Maximum number of tokens in AI responses</div>
              </div>
            </div>

            <div class="setting-card">
              <div class="setting-card-header">
                <i class="fas fa-thermometer-half"></i>
                <span>Creativity</span>
              </div>
              <div class="setting-card-content">
                <div class="temperature-container">
                  <input type="range" id="temperature" min="0" max="1" step="0.1" value="0.7">
                  <span id="temperatureValue">0.7</span>
                </div>
                <div class="temperature-labels">
                  <span>Precise</span>
                  <span>Creative</span>
                </div>
                <div class="setting-hint">Controls randomness in AI responses</div>
              </div>
            </div>
          </div>

          <div class="toggle-settings">
            <div class="toggle-setting">
              <label class="toggle-switch">
                <input type="checkbox" id="autoAnalyze">
                <span class="toggle-slider"></span>
              </label>
              <div class="toggle-content">
                <div class="toggle-label"><i class="fas fa-magic"></i> Auto-analyze page on startup</div>
                <div class="toggle-description">Automatically analyze the current webpage when opening the extension</div>
              </div>
            </div>

            <div class="toggle-setting">
              <label class="toggle-switch">
                <input type="checkbox" id="rememberHistory">
                <span class="toggle-slider"></span>
              </label>
              <div class="toggle-content">
                <div class="toggle-label"><i class="fas fa-history"></i> Remember chat history</div>
                <div class="toggle-description">Save your conversations between browser sessions</div>
              </div>
            </div>
          </div>

          <button id="saveSettings" class="save-btn"><i class="fas fa-save"></i> Save Settings</button>
        </div>
      </section>

      <!-- History Tab Content -->
      <section id="historyContent" class="tab-content">
        <div class="history-header">
          <h2><i class="fas fa-history"></i> Chat History</h2>
          <div class="history-actions">
            <div class="history-search">
              <input type="text" id="historySearch" placeholder="Search conversations...">
              <i class="fas fa-search"></i>
            </div>
            <div class="history-filter">
              <select id="historyFilter">
                <option value="all">All Models</option>
                <option value="openai">OpenAI</option>
                <option value="gemini">Gemini</option>
                <option value="claude">Claude</option>
                <option value="mistral">Mistral</option>
                <option value="meta-llama">Llama</option>
                <option value="cohere">Cohere</option>
                <option value="deepseek">DeepSeek</option>
              </select>
            </div>
          </div>
        </div>

        <div class="history-container">
          <div class="history-list" id="historyList">
            <!-- Sample history item (will be replaced by actual history) -->
            <div class="history-item">
              <div class="history-item-header">
                <div class="history-item-title">
                  <i class="fas fa-comment-alt"></i>
                  Conversation about web development
                </div>
                <div class="history-item-model openai">
                  <i class="fas fa-robot"></i>
                  GPT-4o
                </div>
              </div>
              <div class="history-item-content">
                <div class="history-item-preview">
                  I asked about modern JavaScript frameworks and received a detailed comparison between React, Vue, and Angular with code examples and best practices for each framework.
                </div>
                <div class="history-item-actions">
                  <div class="history-action-btn restore">
                    <i class="fas fa-redo-alt"></i>
                    Restore Chat
                  </div>
                  <div class="history-action-btn delete">
                    <i class="fas fa-trash-alt"></i>
                    Delete
                  </div>
                </div>
              </div>
            </div>

            <div class="history-item">
              <div class="history-item-header">
                <div class="history-item-title">
                  <i class="fas fa-comment-alt"></i>
                  Python data analysis help
                </div>
                <div class="history-item-model claude">
                  <i class="fas fa-comment"></i>
                  Claude 3
                </div>
              </div>
              <div class="history-item-content">
                <div class="history-item-preview">
                  Asked for help with pandas and matplotlib for data visualization. Got examples of how to create different chart types and process CSV data efficiently.
                </div>
                <div class="history-item-actions">
                  <div class="history-action-btn restore">
                    <i class="fas fa-redo-alt"></i>
                    Restore Chat
                  </div>
                  <div class="history-action-btn delete">
                    <i class="fas fa-trash-alt"></i>
                    Delete
                  </div>
                </div>
              </div>
            </div>

            <!-- Empty state (hidden when there are history items) -->
            <div class="empty-history-state" style="display: none;">
              <div class="empty-history-icon">
                <i class="fas fa-comment-slash"></i>
              </div>
              <div class="empty-history-message">No saved conversations yet</div>
              <div class="empty-history-hint">Your chat history will appear here once you start conversations</div>
            </div>
          </div>
        </div>

        <div class="history-footer">
          <button id="clearAllHistory" class="danger-btn"><i class="fas fa-trash-alt"></i> Clear All History</button>
          <div class="history-info">
            <i class="fas fa-info-circle"></i>
            <span>History is stored locally in your browser</span>
          </div>
        </div>
      </section>

      <!-- Stats Tab Content -->
      <section id="statsContent" class="tab-content">
        <div class="stats-header">
          <h2><i class="fas fa-chart-line"></i> Usage Statistics</h2>
        </div>

        <!-- Summary Stats -->
        <div class="stats-overview">
          <div class="stats-summary">
            <div class="summary-card">
              <div class="summary-icon">
                <i class="fas fa-comment-dots"></i>
              </div>
              <div class="summary-label">Total Requests</div>
              <div class="summary-value" id="totalRequests">0</div>
              <div class="summary-subtext">Conversations started</div>
            </div>

            <div class="summary-card">
              <div class="summary-icon">
                <i class="fas fa-microchip"></i>
              </div>
              <div class="summary-label">Total Tokens</div>
              <div class="summary-value" id="totalTokens">0</div>
              <div class="summary-subtext">Used across all models</div>
            </div>

            <div class="summary-card">
              <div class="summary-icon">
                <i class="fas fa-bolt"></i>
              </div>
              <div class="summary-label">Avg. Response Time</div>
              <div class="summary-value" id="avgResponseTime">1.2s</div>
              <div class="summary-subtext">Average response speed</div>
            </div>
          </div>
        </div>

        <!-- Usage Graph -->
        <div class="usage-graph">
          <div class="usage-graph-header">
            <div class="usage-graph-title">
              <i class="fas fa-chart-bar"></i>
              Usage Trends
            </div>
            <div class="usage-graph-controls">
              <div class="graph-period-selector active">Week</div>
              <div class="graph-period-selector">Month</div>
              <div class="graph-period-selector">Year</div>
            </div>
          </div>

          <div class="graph-container">
            <!-- These bar groups will be dynamically generated/updated with JavaScript -->
            <div class="graph-bar-group">
              <div class="graph-bar-tooltip">OpenAI: 50, Gemini: 40, Others: 30</div>
              <div class="graph-bar graph-bar-openai" style="height: 25%;"></div>
              <div class="graph-bar graph-bar-gemini" style="height: 20%;"></div>
              <div class="graph-bar graph-bar-openrouter" style="height: 15%;"></div>
              <div class="graph-bar-label">Mon</div>
            </div>

            <div class="graph-bar-group">
              <div class="graph-bar-tooltip">OpenAI: 70, Gemini: 60, Others: 50</div>
              <div class="graph-bar graph-bar-openai" style="height: 35%;"></div>
              <div class="graph-bar graph-bar-gemini" style="height: 30%;"></div>
              <div class="graph-bar graph-bar-openrouter" style="height: 25%;"></div>
              <div class="graph-bar-label">Tue</div>
            </div>

            <div class="graph-bar-group">
              <div class="graph-bar-tooltip">OpenAI: 100, Gemini: 80, Others: 60</div>
              <div class="graph-bar graph-bar-openai" style="height: 50%;"></div>
              <div class="graph-bar graph-bar-gemini" style="height: 40%;"></div>
              <div class="graph-bar graph-bar-openrouter" style="height: 30%;"></div>
              <div class="graph-bar-label">Wed</div>
            </div>

            <div class="graph-bar-group">
              <div class="graph-bar-tooltip">OpenAI: 150, Gemini: 100, Others: 50</div>
              <div class="graph-bar graph-bar-openai" style="height: 75%;"></div>
              <div class="graph-bar graph-bar-gemini" style="height: 50%;"></div>
              <div class="graph-bar graph-bar-openrouter" style="height: 25%;"></div>
              <div class="graph-bar-label">Thu</div>
            </div>

            <div class="graph-bar-group">
              <div class="graph-bar-tooltip">OpenAI: 180, Gemini: 120, Others: 60</div>
              <div class="graph-bar graph-bar-openai" style="height: 90%;"></div>
              <div class="graph-bar graph-bar-gemini" style="height: 60%;"></div>
              <div class="graph-bar graph-bar-openrouter" style="height: 30%;"></div>
              <div class="graph-bar-label">Fri</div>
            </div>

            <div class="graph-bar-group">
              <div class="graph-bar-tooltip">OpenAI: 100, Gemini: 70, Others: 30</div>
              <div class="graph-bar graph-bar-openai" style="height: 50%;"></div>
              <div class="graph-bar graph-bar-gemini" style="height: 35%;"></div>
              <div class="graph-bar graph-bar-openrouter" style="height: 15%;"></div>
              <div class="graph-bar-label">Sat</div>
            </div>

            <div class="graph-bar-group">
              <div class="graph-bar-tooltip">OpenAI: 50, Gemini: 30, Others: 20</div>
              <div class="graph-bar graph-bar-openai" style="height: 25%;"></div>
              <div class="graph-bar graph-bar-gemini" style="height: 15%;"></div>
              <div class="graph-bar graph-bar-openrouter" style="height: 10%;"></div>
              <div class="graph-bar-label">Sun</div>
            </div>
          </div>

          <div class="graph-legend">
            <div class="legend-item">
              <div class="legend-color legend-openai"></div>
              <span>OpenAI</span>
            </div>
            <div class="legend-item">
              <div class="legend-color legend-gemini"></div>
              <span>Gemini</span>
            </div>
            <div class="legend-item">
              <div class="legend-color legend-openrouter"></div>
              <span>OpenRouter</span>
            </div>

          </div>
        </div>

        <!-- Provider Stats -->
        <div class="stats-section">
          <h3 class="section-title"><i class="fas fa-server"></i> By Provider</h3>
          <div class="stats-container">
            <!-- OpenRouter Stats -->
            <div class="stats-card openrouter">
              <div class="provider-header">
                <div class="provider-icon">
                  <i class="fas fa-network-wired"></i>
                </div>
                <h4>OpenRouter</h4>
              </div>
              <div class="stats-metrics">
                <div class="metric">
                  <div class="metric-label">Requests</div>
                  <div class="metric-value" id="openrouterRequests">0</div>
                </div>
                <div class="metric">
                  <div class="metric-label">Tokens</div>
                  <div class="metric-value" id="openrouterTokens">0</div>
                </div>
              </div>
            </div>



            <!-- Gemini Stats -->
            <div class="stats-card gemini">
              <div class="provider-header">
                <div class="provider-icon">
                  <i class="fas fa-brain"></i>
                </div>
                <h4>Gemini</h4>
              </div>
              <div class="stats-metrics">
                <div class="metric">
                  <div class="metric-label">Requests</div>
                  <div class="metric-value" id="geminiRequests">0</div>
                </div>
                <div class="metric">
                  <div class="metric-label">Tokens</div>
                  <div class="metric-value" id="geminiTokens">0</div>
                </div>
              </div>
            </div>

            <!-- OpenAI Stats -->
            <div class="stats-card openai">
              <div class="provider-header">
                <div class="provider-icon">
                  <i class="fas fa-robot"></i>
                </div>
                <h4>OpenAI</h4>
              </div>
              <div class="stats-metrics">
                <div class="metric">
                  <div class="metric-label">Requests</div>
                  <div class="metric-value" id="openaiRequests">0</div>
                </div>
                <div class="metric">
                  <div class="metric-label">Tokens</div>
                  <div class="metric-value" id="openaiTokens">0</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Model Usage Stats -->
        <div class="stats-section">
          <h3 class="section-title"><i class="fas fa-microchip"></i> Model Usage</h3>
          <div class="models-container">
            <!-- OpenAI Models -->
            <div class="model-column">
              <div class="model-column-header openai">
                <i class="fas fa-robot"></i>
                <h4>OpenAI</h4>
                <div class="model-count">5</div>
              </div>
              <div class="model-list">
                <div class="model-item">
                  <div class="model-name">GPT-4o</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">GPT-4 Turbo</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">GPT-3.5 Turbo</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">GPT-4 Vision</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">DALL-E 3</div>
                  <div class="model-usage">0</div>
                </div>
              </div>
            </div>

            <!-- Anthropic Models -->
            <div class="model-column">
              <div class="model-column-header anthropic">
                <i class="fas fa-comment-alt"></i>
                <h4>Anthropic</h4>
                <div class="model-count">4</div>
              </div>
              <div class="model-list">
                <div class="model-item">
                  <div class="model-name">Claude 3 Opus</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">Claude 3 Sonnet</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">Claude 3 Haiku</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">Claude 2</div>
                  <div class="model-usage">0</div>
                </div>
              </div>
            </div>

            <!-- Gemini Models -->
            <div class="model-column">
              <div class="model-column-header gemini">
                <i class="fas fa-brain"></i>
                <h4>Gemini</h4>
                <div class="model-count">4</div>
              </div>
              <div class="model-list">
                <div class="model-item">
                  <div class="model-name">Gemini 1.5 Flash</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">Gemini 1.5 Flash Latest</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">Gemini 1.5 Flash 8B</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">Gemini 2.0 Flash (Experimental)</div>
                  <div class="model-usage">0</div>
                </div>
              </div>
            </div>



            <!-- Mistral Models -->
            <div class="model-column">
              <div class="model-column-header mistral">
                <i class="fas fa-wind"></i>
                <h4>Mistral AI</h4>
                <div class="model-count">3</div>
              </div>
              <div class="model-list">
                <div class="model-item">
                  <div class="model-name">Mistral Large</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">Mistral Medium</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">Mistral Small</div>
                  <div class="model-usage">0</div>
                </div>
              </div>
            </div>

            <!-- Cohere Models -->
            <div class="model-column">
              <div class="model-column-header cohere">
                <i class="fas fa-link"></i>
                <h4>Cohere</h4>
                <div class="model-count">3</div>
              </div>
              <div class="model-list">
                <div class="model-item">
                  <div class="model-name">Command R</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">Command R+</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">Embed</div>
                  <div class="model-usage">0</div>
                </div>
              </div>
            </div>

            <!-- OpenAI Models -->
            <div class="model-column">
              <div class="model-column-header openai">
                <i class="fas fa-robot"></i>
                <h4>OpenAI</h4>
                <div class="model-count">3</div>
              </div>
              <div class="model-list">
                <div class="model-item">
                  <div class="model-name">GPT-4</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">GPT-3.5 Turbo</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">Claude 3 Opus</div>
                  <div class="model-usage">0</div>
                </div>
              </div>
            </div>

            <!-- Stability AI Models -->
            <div class="model-column">
              <div class="model-column-header stability">
                <i class="fas fa-image"></i>
                <h4>Stability AI</h4>
                <div class="model-count">3</div>
              </div>
              <div class="model-list">
                <div class="model-item">
                  <div class="model-name">Stable Diffusion 3</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">Stable Diffusion XL</div>
                  <div class="model-usage">0</div>
                </div>
                <div class="model-item">
                  <div class="model-name">Stable Video</div>
                  <div class="model-usage">0</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Cost Estimation Section -->
        <div class="cost-estimation">
          <div class="cost-header">
            <div class="cost-title">
              <i class="fas fa-dollar-sign"></i>
              Estimated Costs
            </div>
            <div class="cost-disclaimer">
              Approximate costs based on current usage and provider pricing
            </div>
          </div>

          <div class="cost-summary">
            <div class="cost-card">
              <div class="cost-provider openai">
                <i class="fas fa-robot"></i>
                OpenAI
              </div>
              <div class="cost-value">$0.25</div>
              <div class="cost-label">Estimated cost</div>
            </div>

            <div class="cost-card">
              <div class="cost-provider gemini">
                <i class="fas fa-brain"></i>
                Gemini
              </div>
              <div class="cost-value">$0.12</div>
              <div class="cost-label">Estimated cost</div>
            </div>

            <div class="cost-card">
              <div class="cost-provider anthropic">
                <i class="fas fa-comment-alt"></i>
                Anthropic
              </div>
              <div class="cost-value">$0.18</div>
              <div class="cost-label">Estimated cost</div>
            </div>


          </div>

          <div class="total-cost">
            <div class="total-cost-label">Total Estimated Cost</div>
            <div class="total-cost-value">$0.60</div>
          </div>
        </div>

        <div class="reset-stats">
          <button id="resetStats" class="reset-btn"><i class="fas fa-redo-alt"></i> Reset Statistics</button>
        </div>
      </section>
    </main>

    <footer>
      <div id="statusMessage"><i class="fas fa-info-circle"></i> Ready to help with anything</div>
    </footer>
  </div>

  <!-- Persistent Toggle Menu has been removed -->

  <script src="../scripts/storage-manager.js"></script>
  <script src="../scripts/openrouter-models.js"></script>
  <script src="../scripts/api-manager.js"></script>
  <script src="../scripts/clipboard-helper.js"></script>
  <script src="../scripts/brave-copy-fix-new.js"></script>
  <script src="../scripts/ui-manager.js"></script>
  <script src="../scripts/improved-nlp-manager.js"></script>
  <script src="../scripts/enhanced-nlp-manager.js"></script>
  <script src="../scripts/top-websites-data.js"></script>
  <script src="../scripts/enhanced-website-nlp-manager.js"></script>
  <script src="../scripts/chat-manager.js"></script>
  <script src="../scripts/feature-manager.js"></script>
  <script src="../scripts/blank-page-handler.js"></script>
  <script src="../scripts/history-manager.js"></script>
  <script src="../scripts/export-manager.js"></script>
  <script src="../scripts/website-analyzer.js"></script>
  <script src="../scripts/pdf-processor.js"></script>
  <script src="../scripts/notion-integration.js"></script>
  <script src="../scripts/trello-integration.js"></script>
  <script src="../scripts/google-integration.js"></script>
  <script src="../scripts/productivity-integrations.js"></script>
  <script src="../scripts/productivity-ui-manager.js"></script>
  <script src="../scripts/pdf-dialog.js"></script>
  <script src="../scripts/productivity-dialog.js"></script>
  <script src="../scripts/creative-studio-dialog.js"></script>
  <script src="../scripts/web-mcp.js"></script>

  <script src="../scripts/fingerprint.js"></script>
  <script src="../scripts/file-handler.js"></script>
  <script src="../scripts/search-utils.js"></script>
  <script src="../scripts/webpage-access.js"></script>
  <script src="../scripts/search-query-dataset.js"></script>
  <script src="../scripts/enhanced-intent-classifier.js"></script>
  <!-- Web MCP Dialog - Modern Redesign 2.0 -->
  <div id="webMcpDialog" class="web-mcp-dialog">
    <div class="web-mcp-wrapper">
      <!-- Header with logo and controls -->
      <div class="web-mcp-header">
        <div class="web-mcp-title">
          <img src="../images/icon.png" alt="Browzy AI" class="web-mcp-logo">
          <div class="web-mcp-title-text">
            <h3>Web MCP</h3>
            <span class="web-mcp-subtitle">Page Analysis & Chat</span>
          </div>
        </div>
        <div class="web-mcp-controls">
          <button id="scanPageBtn" class="web-mcp-btn primary"><i class="fas fa-search"></i> Scan Page</button>
          <button id="mcpClearChatBtn" class="web-mcp-btn secondary"><i class="fas fa-trash-alt"></i></button>
          <button id="closeWebMcpDialog" class="web-mcp-btn close"><i class="fas fa-times"></i></button>
        </div>
      </div>

      <!-- Main content area - Enhanced with better visuals -->
      <div class="web-mcp-body">
        <!-- Status bar with enhanced visuals -->
        <div class="web-mcp-status-bar" id="mcpStatusIndicator">
          <div class="status-icon"><i class="fas fa-circle-notch"></i></div>
          <div class="status-text" id="mcpStatusText">Ready to scan page</div>
          <div class="status-actions" id="mcpStatusActions">
            <button id="mcpCancelScanBtn" class="status-action-btn" style="display: none;"><i class="fas fa-stop"></i> Cancel</button>
          </div>
        </div>

        <!-- Chat area with enhanced visuals -->
        <div class="web-mcp-chat-container">
          <div class="web-mcp-chat-messages" id="mcpChatMessages">
            <div class="mcp-message ai">
              <div class="message-avatar">
                <img src="../images/icon.png" alt="Browzy AI">
              </div>
              <div class="message-content">
                <p>👋 I can analyze this webpage and answer questions about its content. Click <strong>"Scan Page"</strong> to begin.</p>
              </div>
            </div>
          </div>

          <div class="web-mcp-chat-input">
            <textarea id="mcpUserInput" placeholder="Ask about this page..." rows="1"></textarea>
            <button id="mcpSendBtn" class="web-mcp-send-btn"><i class="fas fa-arrow-up"></i></button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Keyboard Shortcuts Dialog -->
  <div id="keyboardShortcutsDialog" class="shortcuts-dialog">
    <div class="shortcuts-content">
      <div class="shortcuts-header">
        <h3><i class="fas fa-keyboard"></i> Keyboard Shortcuts</h3>
        <button id="closeShortcutsDialog" class="shortcuts-close-btn"><i class="fas fa-times"></i></button>
      </div>
      <div class="shortcuts-body">
        <div class="shortcuts-section">
          <h4 class="shortcuts-section-title">Navigation</h4>
          <div class="shortcuts-list">
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>1</kbd></div>
              <div class="shortcut-description">Switch to Chat tab</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>2</kbd></div>
              <div class="shortcut-description">Switch to History tab</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>3</kbd></div>
              <div class="shortcut-description">Switch to Stats tab</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>4</kbd></div>
              <div class="shortcut-description">Switch to Settings tab</div>
            </div>
          </div>
        </div>

        <div class="shortcuts-section">
          <h4 class="shortcuts-section-title">Chat Controls</h4>
          <div class="shortcuts-list">
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>/</kbd></div>
              <div class="shortcut-description">Focus on chat input</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Enter</kbd></div>
              <div class="shortcut-description">Send message</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Shift</kbd> + <kbd>Enter</kbd></div>
              <div class="shortcut-description">New line in chat input</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>C</kbd></div>
              <div class="shortcut-description">Clear chat</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Esc</kbd></div>
              <div class="shortcut-description">Stop AI generation</div>
            </div>
          </div>
        </div>

        <div class="shortcuts-section">
          <h4 class="shortcuts-section-title">Tools & Features</h4>
          <div class="shortcuts-list">
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>W</kbd></div>
              <div class="shortcut-description">Website Analyzer</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>S</kbd></div>
              <div class="shortcut-description">AI Agent Browser</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>P</kbd></div>
              <div class="shortcut-description">PDF Tools</div>
            </div>
            <div class="shortcut-card">
              <div class="shortcut-key"><kbd>Alt</kbd> + <kbd>D</kbd></div>
              <div class="shortcut-description">Destress Mode</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- GSAP Animation Library (local version) -->
  <script src="../lib/gsap.min.js"></script>

  <!-- Web MCP Enhanced Styles -->
  <link rel="stylesheet" href="web-mcp-styles.css">

  <!-- Coming Soon Popup Styles -->
  <link rel="stylesheet" href="coming-soon-popup.css">

  <!-- Destress Mode Enhanced Styles -->
  <link rel="stylesheet" href="destress-mode.css">

  <!-- Translation UI Enhanced Styles -->
  <link rel="stylesheet" href="translate-ui.css">

  <!-- PDF Tools Enhanced Styles -->
  <link rel="stylesheet" href="pdf-tools-ui.css">

  <!-- Scan Tabs Enhanced Styles -->
  <link rel="stylesheet" href="scan-tabs-ui.css">

  <!-- Load all dependencies before popup.js -->
  <script src="../scripts/dashboard-connector.js"></script>
  <script src="../scripts/destress-mode.js"></script>
  <script src="../scripts/translate-ui-manager.js"></script>
  <script src="../scripts/saved-links.js"></script>

  <!-- Load sidebar manager first to ensure it's available -->
  <script src="../scripts/sidebar-manager.js"></script>

  <script src="../scripts/keyboard-shortcuts.js"></script>

  <!-- Settings and History UI enhancements -->
  <script src="settings-history-ui.js"></script>

  <!-- Load stop button script -->
  <script src="stop-button.js"></script>

  <!-- Load sidebar buttons script for close button functionality -->
  <script src="sidebar-buttons.js"></script>

  <!-- AI Agent Browser Scripts -->
  <script src="../scripts/google-advanced-search.js"></script>
  <script src="../scripts/ai-agent-browser.js"></script>
  <script src="../scripts/ai-agent-demo.js"></script>

  <!-- Load popup.js last to ensure all dependencies are loaded -->
  <script src="popup.js"></script>

  <!-- Coming Soon Popup for Web MCP -->
  <div id="comingSoonPopup" class="coming-soon-popup">
    <div class="coming-soon-wrapper">
      <!-- Header with logo and controls -->
      <div class="coming-soon-header">
        <div class="coming-soon-title">
          <img src="../images/icon.png" alt="Browzy AI" class="coming-soon-logo">
          <div class="coming-soon-title-text">
            <h3>Web MCP</h3>
            <span class="coming-soon-subtitle">Advanced Page Analysis & Chat</span>
          </div>
        </div>
        <button id="closeComingSoonPopup" class="coming-soon-close"><i class="fas fa-times"></i></button>
      </div>

      <!-- Body with coming soon message -->
      <div class="coming-soon-body">
        <div class="coming-soon-icon">
          <i class="fas fa-rocket"></i>
        </div>
        <div class="coming-soon-message">Coming Soon!</div>
        <div class="coming-soon-description">
          We're working hard to bring you the Web MCP feature. This powerful tool will allow you to analyze web pages and chat with AI about their content with unprecedented depth and accuracy.
        </div>
        <div class="feature-highlights">
          <div class="feature-item">
            <i class="fas fa-search"></i>
            <span>Deep page analysis</span>
          </div>
          <div class="feature-item">
            <i class="fas fa-brain"></i>
            <span>AI-powered insights</span>
          </div>
          <div class="feature-item">
            <i class="fas fa-comments"></i>
            <span>Contextual chat</span>
          </div>
        </div>
        <button id="notifyMeButton" class="coming-soon-btn">
          <i class="fas fa-bell"></i> Notify Me When Available
        </button>
      </div>
    </div>
  </div>

  <!-- Coming Soon Popup for Upload from Computer -->
  <div id="uploadComingSoonPopup" class="coming-soon-popup">
    <div class="coming-soon-wrapper">
      <!-- Header with logo and controls -->
      <div class="coming-soon-header">
        <div class="coming-soon-title">
          <img src="../images/icon.png" alt="Browzy AI" class="coming-soon-logo">
          <div class="coming-soon-title-text">
            <h3>Upload from Computer</h3>
            <span class="coming-soon-subtitle">Smart File Analysis & Chat</span>
          </div>
        </div>
        <button id="closeUploadComingSoonPopup" class="coming-soon-close"><i class="fas fa-times"></i></button>
      </div>

      <!-- Body with coming soon message -->
      <div class="coming-soon-body">
        <div class="coming-soon-icon">
          <i class="fas fa-file-upload"></i>
        </div>
        <div class="coming-soon-message">Coming Soon!</div>
        <div class="coming-soon-description">
          We're working hard to bring you the Upload from Computer feature. This powerful tool will allow you to upload and analyze files directly from your computer with advanced AI capabilities.
        </div>
        <div class="feature-highlights">
          <div class="feature-item">
            <i class="fas fa-file-pdf"></i>
            <span>PDF analysis</span>
          </div>
          <div class="feature-item">
            <i class="fas fa-file-code"></i>
            <span>Code understanding</span>
          </div>
          <div class="feature-item">
            <i class="fas fa-file-csv"></i>
            <span>Data extraction</span>
          </div>
        </div>
        <button id="notifyMeUploadButton" class="coming-soon-btn">
          <i class="fas fa-bell"></i> Notify Me When Available
        </button>
      </div>
    </div>
  </div>
</body>
</html>

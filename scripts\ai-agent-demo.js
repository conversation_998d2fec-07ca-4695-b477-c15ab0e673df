'use strict';

/**
 * AI Agent Browser Demo
 * Demonstrates the capabilities of the AI Agent Browser
 */
class AIAgentDemo {
  constructor() {
    this.demoQueries = [
      {
        query: "What are the latest developments in artificial intelligence?",
        description: "News and current events query",
        expectedIntent: "news"
      },
      {
        query: "How to learn Python programming for beginners",
        description: "Tutorial/how-to query",
        expectedIntent: "howTo"
      },
      {
        query: "Compare React vs Vue.js for web development",
        description: "Comparison query",
        expectedIntent: "comparison"
      },
      {
        query: "What is quantum computing?",
        description: "Definition query",
        expectedIntent: "definition"
      },
      {
        query: "Best practices for machine learning model deployment",
        description: "Technical query",
        expectedIntent: "technical"
      }
    ];
  }

  /**
   * Run a demo of the AI Agent Browser
   * @param {APIManager} apiManager - The API manager instance
   * @returns {Promise<void>}
   */
  async runDemo(apiManager) {
    console.log('🤖 AI Agent Browser Demo Starting...');
    
    if (!window.AIAgentBrowser) {
      console.error('❌ AI Agent Browser not loaded');
      return;
    }

    const aiAgent = new AIAgentBrowser(apiManager);
    
    console.log('📋 Available Demo Queries:');
    this.demoQueries.forEach((demo, index) => {
      console.log(`${index + 1}. ${demo.query} (${demo.description})`);
    });

    // Run a sample query
    const sampleQuery = this.demoQueries[0];
    console.log(`\n🔍 Running demo query: "${sampleQuery.query}"`);
    
    try {
      const result = await aiAgent.browse(sampleQuery.query, (progress) => {
        console.log(`📊 Progress: ${progress.stage} - ${progress.message}`);
      });

      if (result.success) {
        console.log('✅ Demo completed successfully!');
        console.log('📊 Results:', {
          query: result.query,
          intent: result.analysis?.intent,
          sourcesFound: result.sources?.length || 0,
          confidence: result.answer?.confidence || 0,
          searchQueries: result.searchQueries?.length || 0
        });
      } else {
        console.log('❌ Demo failed:', result.error);
      }
    } catch (error) {
      console.error('❌ Demo error:', error);
    }
  }

  /**
   * Test the Google Advanced Search functionality
   */
  testAdvancedSearch() {
    console.log('🔍 Testing Google Advanced Search...');
    
    if (!window.GoogleAdvancedSearch) {
      console.error('❌ Google Advanced Search not loaded');
      return;
    }

    const advancedSearch = new GoogleAdvancedSearch();
    
    // Test query analysis
    const testAnalysis = {
      intent: 'academic',
      entities: ['machine learning', 'neural networks'],
      keywords: ['machine', 'learning', 'neural', 'networks'],
      timeframe: 'recent',
      searchType: 'factual'
    };

    const queries = advancedSearch.generateAdvancedQueries(testAnalysis);
    
    console.log('📋 Generated Advanced Search Queries:');
    queries.forEach((query, index) => {
      console.log(`${index + 1}. ${query.query} (${query.type})`);
    });

    // Test dorking query builder
    const dorkingQuery = advancedSearch.buildDorkingQuery({
      keywords: ['machine learning'],
      site: 'arxiv.org',
      fileType: 'pdf',
      inTitle: 'neural networks',
      dateAfter: '2023-01-01'
    });

    console.log(`🎯 Sample Dorking Query: ${dorkingQuery}`);
  }

  /**
   * Show AI Agent capabilities
   */
  showCapabilities() {
    console.log('🤖 AI Agent Browser Capabilities:');
    console.log('');
    console.log('🧠 Query Understanding:');
    console.log('  • Uses LLM to analyze user intent');
    console.log('  • Extracts entities and keywords');
    console.log('  • Determines optimal search strategy');
    console.log('');
    console.log('🔍 Advanced Search:');
    console.log('  • Google dorking techniques');
    console.log('  • Site-specific searches');
    console.log('  • File type filtering');
    console.log('  • Date range restrictions');
    console.log('');
    console.log('📊 Content Analysis:');
    console.log('  • Fetches content from top results');
    console.log('  • Extracts key information using LLM');
    console.log('  • Scores source reliability');
    console.log('  • Removes duplicate content');
    console.log('');
    console.log('🎯 Response Generation:');
    console.log('  • Synthesizes information from multiple sources');
    console.log('  • Provides source citations');
    console.log('  • Suggests follow-up questions');
    console.log('  • Includes confidence scores');
    console.log('');
    console.log('🚀 Features:');
    console.log('  • Real-time progress updates');
    console.log('  • Fallback to simple search');
    console.log('  • Search history tracking');
    console.log('  • Error handling and recovery');
  }

  /**
   * Test search operators
   */
  testSearchOperators() {
    console.log('🔧 Testing Search Operators...');
    
    if (!window.GoogleAdvancedSearch) {
      console.error('❌ Google Advanced Search not loaded');
      return;
    }

    const advancedSearch = new GoogleAdvancedSearch();
    
    console.log('📋 Available Search Operators:');
    Object.keys(advancedSearch.operators).forEach(operator => {
      console.log(`  • ${operator}: ${advancedSearch.operators[operator]('example')}`);
    });

    console.log('\n🎯 Example Queries:');
    console.log(`  • Academic: ${advancedSearch.operators.site('arxiv.org')} machine learning ${advancedSearch.operators.filetype('pdf')}`);
    console.log(`  • News: ${advancedSearch.operators.after('2024-01-01')} artificial intelligence`);
    console.log(`  • Technical: ${advancedSearch.operators.site('stackoverflow.com')} ${advancedSearch.operators.intitle('python error')}`);
    console.log(`  • Definition: ${advancedSearch.operators.define('quantum computing')}`);
  }
}

// Export for use in console
window.AIAgentDemo = AIAgentDemo;

// Auto-run demo if in development mode
if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
  console.log('🚀 AI Agent Browser Demo loaded! Use the following commands:');
  console.log('');
  console.log('const demo = new AIAgentDemo();');
  console.log('demo.showCapabilities();');
  console.log('demo.testAdvancedSearch();');
  console.log('demo.testSearchOperators();');
  console.log('demo.runDemo(apiManager); // Requires API manager');
  console.log('');
}

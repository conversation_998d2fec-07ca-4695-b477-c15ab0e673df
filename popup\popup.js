// Function to play the popup animation using GSAP
function playPopupAnimation() {
  // Get the animation overlay elements
  const animationOverlay = document.getElementById('animationOverlay');
  if (!animationOverlay) {
    console.warn('Animation overlay not found, skipping animation');
    return;
  }

  const logo = animationOverlay.querySelector('.animated-logo');
  const welcomeText = animationOverlay.querySelector('.welcome-text');
  const loadingIndicator = animationOverlay.querySelector('.loading-indicator-animation');
  const container = document.querySelector('.container');
  const particles = animationOverlay.querySelectorAll('.particle');
  const glowEffect = animationOverlay.querySelector('.glow-effect');
  const pulseRing = animationOverlay.querySelector('.pulse-ring');
  const dots = animationOverlay.querySelectorAll('.dot');
  const letters = animationOverlay.querySelectorAll('.letter');

  // Create a GSAP timeline for the animation sequence with a total duration of 8 seconds
  const tl = gsap.timeline({
    onComplete: () => {
      // Hide the overlay when animation is complete
      gsap.to(animationOverlay, {
        opacity: 0,
        duration: 0.8,
        ease: "power2.inOut",
        onComplete: () => {
          animationOverlay.style.display = 'none';
        }
      });
    }
  });

  // Set initial states
  gsap.set(container, { opacity: 0 });
  gsap.set(animationOverlay, { opacity: 1, display: 'flex' });
  gsap.set(particles, { opacity: 0, scale: 0 });
  gsap.set(glowEffect, { opacity: 0, scale: 0.5 });
  gsap.set(pulseRing, { opacity: 0, scale: 0.5 });
  gsap.set(dots, { opacity: 0, y: 10 });
  gsap.set(letters, { opacity: 0, y: 10 });

  // Background animation with enhanced gradient
  gsap.fromTo(animationOverlay,
    {
      background: 'linear-gradient(135deg, #050a0f 0%, #0a1520 100%)'
    },
    {
      background: 'linear-gradient(135deg, #0a0f14 0%, #121820 100%)',
      duration: 8,
      ease: "power1.inOut"
    }
  );

  // Particles animation setup with improved randomness and timing
  particles.forEach((particle, index) => {
    // Random positions for particles with wider range
    const randomX = gsap.utils.random(-180, 180);
    const randomY = gsap.utils.random(-180, 180);
    const randomDelay = index * 0.1; // Reduced delay for faster appearance
    const randomDuration = gsap.utils.random(15, 30); // Faster movement
    const randomOpacity = gsap.utils.random(0.5, 0.9); // Higher opacity

    // Add particle animations to timeline with varied scales
    tl.to(particle, {
      opacity: randomOpacity,
      scale: gsap.utils.random(0.9, 1.4), // Larger scale
      duration: 1,
      delay: randomDelay,
      ease: "power2.out"
    }, 0.2);

    // Add floating animation with more variety
    gsap.to(particle, {
      x: randomX,
      y: randomY,
      duration: randomDuration,
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });

    // Add subtle scale pulsing for all particles with varied timing
    gsap.to(particle, {
      scale: gsap.utils.random(0.9, 1.5),
      duration: gsap.utils.random(1.5, 3),
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut",
      delay: randomDelay
    });
  });

  // Enhanced animation sequence with precise timing to fit within 8 seconds
  tl.to(glowEffect, {
    opacity: 0.9, // Increased from 0.8
    scale: 1.2, // Increased from 1
    duration: 1.2,
    ease: "power2.out"
  }, 0)

    // Add pulse ring animation with enhanced effect
    .fromTo(pulseRing,
      { opacity: 0, scale: 0.5 },
      {
        opacity: 0.8, // Increased from 0.7
        scale: 1.1, // Increased from 1
        duration: 1,
        ease: "power2.out",
        onComplete: () => {
          // Add continuous pulse animation
          gsap.to(pulseRing, {
            scale: 1.8, // Increased from 1.5
            opacity: 0,
            duration: 1.5, // Faster animation
            repeat: -1,
            ease: "power1.out",
            repeatDelay: 0.3 // Reduced delay
          });
        }
      }, 0.1)

    // Logo animation with enhanced effects
    .fromTo(logo,
      { opacity: 0, scale: 0.5, rotation: -15 }, // Increased rotation
      {
        opacity: 1,
        scale: 1.1, // Slightly larger
        rotation: 0,
        duration: 1,
        ease: "back.out(1.7)",
        onComplete: () => {
          // Add subtle continuous rotation
          gsap.to(logo, {
            rotation: 5,
            duration: 2,
            repeat: -1,
            yoyo: true,
            ease: "sine.inOut"
          });
        }
      },
      0.2
    )

    // Logo glow pulse
    .to(logo, {
      filter: 'drop-shadow(0 0 25px rgba(0, 216, 224, 0.9))',
      duration: 1.5,
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    }, 1.2)

    // Logo bounce with enhanced movement
    .fromTo(logo,
      { y: 0 },
      { y: -20, duration: 0.7, repeat: 1, yoyo: true, ease: "power1.inOut" },
      1
    )

    // Animate letters with stagger and enhanced effects
    .to(letters, {
      opacity: 1,
      y: 0,
      stagger: 0.04, // Faster stagger
      duration: 0.3,
      ease: "back.out(1.7)",
      onComplete: () => {
        // Add subtle hover animation to letters
        gsap.to(letters, {
          y: -4, // Increased movement
          duration: 1.2,
          stagger: 0.08,
          repeat: -1,
          yoyo: true,
          ease: "sine.inOut"
        });

        // Add underline animation to each letter
        letters.forEach((letter, index) => {
          if (!letter.classList.contains('space')) {
            gsap.to(letter, {
              onComplete: () => {
                const underline = document.createElement('div');
                underline.style.position = 'absolute';
                underline.style.bottom = '-4px';
                underline.style.left = '0';
                underline.style.width = '100%';
                underline.style.height = '2px';
                underline.style.background = 'var(--primary-color)';
                underline.style.transform = 'scaleX(0)';
                underline.style.transformOrigin = 'left';
                underline.style.opacity = '0';
                letter.style.position = 'relative';
                letter.appendChild(underline);

                gsap.to(underline, {
                  scaleX: 1,
                  opacity: 0.7,
                  duration: 0.3,
                  delay: index * 0.04,
                  ease: "power1.inOut"
                });
              }
            });
          }
        });
      }
    }, 1.5)

    // Welcome text animation with enhanced effects
    .fromTo(welcomeText,
      { opacity: 0, y: 25, scale: 0.9 },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.7,
        ease: "back.out(1.5)"
      },
      2.2
    )

    // Add glow effect to welcome text
    .to(welcomeText, {
      textShadow: '0 0 15px rgba(0, 216, 224, 0.6)',
      duration: 1.5,
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    }, 2.9)

    // Loading indicator animation with enhanced timing
    .fromTo(loadingIndicator,
      { opacity: 0, y: 15 },
      { opacity: 1, y: 0, duration: 0.4, ease: "power1.out" },
      2.7
    )

    // Dots animation with enhanced effects
    .to(dots, {
      opacity: 1,
      y: 0,
      stagger: 0.1, // Faster stagger
      duration: 0.3,
      ease: "power1.inOut",
      onComplete: () => {
        // Add pulsing animation to dots with varied timing
        dots.forEach((dot, index) => {
          gsap.to(dot, {
            scale: 1.4, // Larger scale
            duration: 0.5,
            delay: index * 0.1,
            repeat: -1,
            yoyo: true,
            ease: "sine.inOut"
          });

          // Add color shift animation
          gsap.to(dot, {
            backgroundColor: 'var(--primary-light)',
            boxShadow: '0 0 15px rgba(0, 216, 224, 0.8)',
            duration: 1,
            delay: index * 0.1,
            repeat: -1,
            yoyo: true,
            ease: "sine.inOut"
          });
        });
      }
    }, 2.8)

    // Show container with precise timing to ensure 8-second total duration
    .to(container, {
      opacity: 1,
      duration: 0.8,
      ease: "power2.inOut"
    }, 6.5); // Start at 6.5 seconds to ensure completion by 8 seconds
}

document.addEventListener('DOMContentLoaded', async () => {
  'use strict';

  // Add debugging
  console.log('DOMContentLoaded event fired');
  console.log('Checking if StorageManager is defined:', typeof StorageManager);
  console.log('Checking if APIManager is defined:', typeof APIManager);
  console.log('Checking if UIManager is defined:', typeof UIManager);

  // Check if we're in sidebar mode and if this is a blank page
  const urlParams = new URLSearchParams(window.location.search);
  const initialSidebarCheck = urlParams.get('sidebar');
  const initialSidebarMode = initialSidebarCheck === 'true';
  const isBlankPage = urlParams.get('blankPage') === 'true';
  const isNewTab = urlParams.get('newTab') === 'true';

  console.log('URL parameters:', {
    sidebarMode: initialSidebarMode,
    isBlankPage: isBlankPage,
    isNewTab: isNewTab,
    fullUrl: window.location.href
  });

  // Special handling for blank pages - add classes immediately
  if (isBlankPage) {
    console.log('Blank page detected, adding special classes');
    document.body.classList.add('blank-page-sidebar');

    if (isNewTab) {
      console.log('New tab detected, adding special class');
      document.body.classList.add('new-tab-sidebar');
    }

    // Create a global variable to indicate this is a blank page
    window.isBlankPage = true;
    window.isNewTab = isNewTab;

    // Force load all critical scripts for blank pages
    const criticalScripts = [
      'scripts/blank-page-handler.js',
      'scripts/sidebar-manager.js',
      'scripts/chat-manager.js',
      'scripts/api-manager.js',
      'scripts/ui-manager.js'
    ];

    // Function to load a script and return a promise
    const loadScript = (src) => {
      return new Promise((resolve, reject) => {
        if (document.querySelector(`script[src*="${src}"]`)) {
          console.log(`Script ${src} already loaded, skipping`);
          resolve();
          return;
        }

        console.log(`Forcing load of ${src}`);
        const scriptElement = document.createElement('script');
        scriptElement.src = chrome.runtime.getURL(src);
        scriptElement.onload = () => {
          console.log(`${src} loaded successfully`);
          resolve();
        };
        scriptElement.onerror = (error) => {
          console.error(`Error loading ${src}:`, error);
          reject(error);
        };
        document.head.appendChild(scriptElement);
      });
    };

    // Load all critical scripts in sequence
    const loadAllScripts = async () => {
      try {
        for (const script of criticalScripts) {
          await loadScript(script);
        }

        // After all scripts are loaded, initialize everything
        console.log('All critical scripts loaded, initializing components');

        // Add initialization script
        const initScript = document.createElement('script');
        initScript.textContent = `
          // Initialize all critical components
          try {
            console.log('Initializing critical components on blank page');

            // Initialize SidebarManager if it exists
            if (typeof SidebarManager === 'function' && !window.sidebarManager) {
              console.log('Creating SidebarManager instance');
              window.sidebarManager = new SidebarManager();
              if (typeof window.sidebarManager.init === 'function') {
                window.sidebarManager.init();
              }
            }

            // Initialize BlankPageHandler if it exists
            if (typeof BlankPageHandler === 'function' && !window.blankPageHandler) {
              console.log('Creating BlankPageHandler instance');
              window.blankPageHandler = new BlankPageHandler();
            } else if (window.blankPageHandler && typeof window.blankPageHandler.init === 'function') {
              console.log('BlankPageHandler exists, calling init()');
              window.blankPageHandler.init();
            }

            // Initialize ChatManager if it exists
            if (typeof ChatManager === 'function' && !window.chatManager) {
              console.log('Creating ChatManager instance');
              window.chatManager = new ChatManager();
              if (typeof window.chatManager.init === 'function') {
                window.chatManager.init();
              }
            }

            // Initialize APIManager if it exists
            if (typeof APIManager === 'function' && !window.apiManager) {
              console.log('Creating APIManager instance');
              window.apiManager = new APIManager();
              if (typeof window.apiManager.init === 'function') {
                window.apiManager.init();
              }
            }

            // Initialize UIManager if it exists
            if (typeof UIManager === 'function' && !window.uiManager) {
              console.log('Creating UIManager instance');
              window.uiManager = new UIManager();
              if (typeof window.uiManager.init === 'function') {
                window.uiManager.init();
              }
            }

            console.log('All critical components initialized on blank page');
          } catch (error) {
            console.error('Error initializing components on blank page:', error);
          }
        `;
        document.head.appendChild(initScript);

      } catch (error) {
        console.error('Error loading critical scripts:', error);
      }
    };

    // Call immediately and also after delays
    loadAllScripts();
    setTimeout(loadAllScripts, 1000);
    setTimeout(loadAllScripts, 2000);
  }

  // Play the animation for popup mode (not sidebar)
  if (!initialSidebarMode) {
    // Show animation overlay
    const animationOverlay = document.getElementById('animationOverlay');
    if (animationOverlay) {
      animationOverlay.style.opacity = '1';
      animationOverlay.style.display = 'flex';

      // If GSAP is available, use it for animation
      if (typeof gsap !== 'undefined') {
        playPopupAnimation();
      } else {
        // Fallback to CSS animation
        animationOverlay.classList.add('active');
        setTimeout(() => {
          animationOverlay.style.opacity = '0';
          setTimeout(() => {
            animationOverlay.style.display = 'none';
          }, 500);
        }, 2500);
      }
    }
  }

  // Declare managers at a higher scope
  let storage, apiManager, uiManager, chatManager, historyManager, exportManager, featureManager, sidebarManager;

  try {
    // Initialize managers
    console.log('Initializing StorageManager');
    storage = new StorageManager();
    await storage.init();
    console.log('StorageManager initialized successfully');

    console.log('Initializing APIManager');
    apiManager = new APIManager(storage);
    console.log('APIManager initialized successfully');

    console.log('Initializing UIManager');
    uiManager = new UIManager();
    console.log('UIManager initialized successfully');
  } catch (error) {
    console.error('Error initializing managers:', error);
    // Show error in UI
    document.getElementById('statusMessage').innerHTML = `<i class="fas fa-exclamation-triangle"></i> Error: ${error.message}`;
    return; // Stop execution if managers couldn't be initialized
  }



  // Ensure content script is loaded in the active tab
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tab) {
      // We're not skipping any URLs now to ensure content script is loaded everywhere
      console.log('Attempting to inject content script for all pages');

      // Verify the tab still exists before proceeding
      try {
        await chrome.tabs.get(tab.id);

        // Check if content script is already loaded
        try {
          await chrome.tabs.sendMessage(tab.id, { action: 'ping' });
          console.log('Content script is already loaded');
        } catch (error) {
          console.log('Content script not loaded, injecting it now...');
          // Inject content script if not already loaded
          try {
            await chrome.scripting.executeScript({
              target: { tabId: tab.id },
              files: ['content/content.js']
            });
            console.log('Content script injected successfully');
            // Wait a moment for the script to initialize
            await new Promise(resolve => setTimeout(resolve, 300));
          } catch (injectionError) {
            console.error('Failed to inject content script:', injectionError);
            uiManager.showStatus('Could not access page content. Try refreshing the page.', true);
          }
        }
      } catch (tabError) {
        console.warn(`Tab ${tab.id} no longer exists or cannot be accessed:`, tabError);
      }
    }
  } catch (error) {
    console.error('Error checking content script:', error);
  }

  // Initialize the managers
  featureManager = new FeatureManager(apiManager, uiManager);
  chatManager = new ChatManager(apiManager, uiManager, featureManager);
  historyManager = new HistoryManager(storage, chatManager, uiManager);
  exportManager = new ExportManager(chatManager, uiManager);

  // Initialize sidebar manager if the class is available
  try {
    if (typeof SidebarManager === 'function') {
      sidebarManager = new SidebarManager();
      console.log('SidebarManager initialized successfully');
    } else {
      console.warn('SidebarManager class not found, skipping initialization');
    }
  } catch (error) {
    console.error('Error initializing SidebarManager:', error);
  }

  // Make export manager available to chat manager
  chatManager.exportManager = exportManager;

  // Function to initialize UI elements for blank pages
  const initializeBlankPageUI = () => {
    console.log('Initializing UI elements for blank page sidebar');

    try {
      // Make sure all manager objects are available in the global scope
      if (chatManager) window.chatManager = chatManager;
      if (historyManager) window.historyManager = historyManager;
      if (exportManager) window.exportManager = exportManager;
      if (featureManager) window.featureManager = featureManager;
      if (uiManager) window.uiManager = uiManager;
      if (apiManager) window.apiManager = apiManager;

      // Make updateStatsDisplay available globally
      if (typeof updateStatsDisplay === 'function') {
        window.updateStatsDisplay = updateStatsDisplay;
      }

      console.log('Made all manager objects available globally');

      // Add classes to the body to ensure proper styling
      document.body.classList.add('blank-page-sidebar');
      if (isNewTab) {
        document.body.classList.add('new-tab-sidebar');
      }

      // Set global flags
      window.isBlankPage = true;
      window.isNewTab = isNewTab;

      // Initialize chat input
      const userInput = document.getElementById('userInput');
      if (userInput) {
        // Add a data attribute to track initialization
        if (!userInput.hasAttribute('data-initialized')) {
          userInput.setAttribute('data-initialized', 'true');
          console.log('Initializing userInput element');

          // Force the input to be visible and interactive
          userInput.style.pointerEvents = 'auto';
          userInput.style.zIndex = '9999';
          userInput.style.position = 'relative';
          userInput.style.display = 'block';
          userInput.style.visibility = 'visible';
          userInput.style.opacity = '1';

          userInput.focus();

          // Make sure the input event listener is attached
          userInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
          });

          // Add keydown event listener for Enter key
          userInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              console.log('Enter key pressed in blank page sidebar');

              // Store the message text in case we need to use it directly
              const messageText = this.value.trim();
              if (!messageText) return;

              // Try multiple methods to send the message
              try {
                // Method 1: Direct call to chat manager
                if (window.chatManager && typeof window.chatManager.sendMessage === 'function') {
                  console.log('Using direct chat manager call');
                  window.chatManager.sendMessage();
                }
              } catch (error) {
                console.error('Error calling chatManager.sendMessage', error);
              }

              try {
                // Method 2: Custom event
                console.log('Dispatching sendMessage event');
                const event = new CustomEvent('sendMessage', {
                  detail: { message: messageText }
                });
                document.dispatchEvent(event);
              } catch (error) {
                console.error('Error dispatching sendMessage event', error);
              }

              try {
                // Method 3: Trigger click on send button
                console.log('Clicking send button');
                const sendButton = document.getElementById('sendMessage');
                if (sendButton) {
                  sendButton.click();
                }
              } catch (error) {
                console.error('Error clicking send button', error);
              }

              // Method 4: Direct manipulation of chat UI as a last resort
              try {
                if (!window.chatManager && document.getElementById('chatMessages')) {
                  console.log('Using direct UI manipulation as fallback');

                  // Add user message to chat
                  const chatMessages = document.getElementById('chatMessages');
                  const userMessageDiv = document.createElement('div');
                  userMessageDiv.className = 'message user-message';
                  userMessageDiv.innerHTML = `<div class="message-content">${messageText}</div>`;
                  chatMessages.appendChild(userMessageDiv);

                  // Clear input
                  this.value = '';

                  // Add AI response
                  const aiMessageDiv = document.createElement('div');
                  aiMessageDiv.className = 'message ai-message';
                  aiMessageDiv.innerHTML = `<div class="message-content">I'm sorry, but the chat functionality isn't fully initialized yet. Please try again in a moment.</div>`;
                  chatMessages.appendChild(aiMessageDiv);

                  // Scroll to bottom
                  chatMessages.scrollTop = chatMessages.scrollHeight;
                }
              } catch (error) {
                console.error('Error with direct UI manipulation', error);
              }
            }
          });

          console.log('Added event listeners to userInput');
        } else {
          console.log('userInput already initialized');
        }
      } else {
        console.error('userInput element not found');
      }

      // Initialize all buttons with direct event handlers
      const allButtons = document.querySelectorAll('button');
      console.log(`Found ${allButtons.length} buttons to initialize`);

      allButtons.forEach(button => {
        // Add a data attribute to track initialization
        if (!button.hasAttribute('data-initialized')) {
          button.setAttribute('data-initialized', 'true');

          // Force the button to be visible and interactive
          button.style.pointerEvents = 'auto';
          button.style.cursor = 'pointer';
          button.style.zIndex = '9999';
          button.style.position = 'relative';
          button.style.display = 'block';
          button.style.visibility = 'visible';
          button.style.opacity = '1';

          // Add a direct click handler
          button.addEventListener('click', function(event) {
            // Prevent default to ensure our handler runs
            event.preventDefault();
            event.stopPropagation();

            console.log('Button clicked:', button.id || button.className);

            // Special handling for specific buttons
            if (button.id === 'sendMessage') {
              console.log('Send button clicked in blank page sidebar');
              const userInput = document.getElementById('userInput');
              if (userInput && userInput.value.trim()) {
                // Store the message text in case we need to use it directly
                const messageText = userInput.value.trim();

                // Try multiple methods to send the message
                try {
                  // Method 1: Direct call to chat manager
                  if (window.chatManager && typeof window.chatManager.sendMessage === 'function') {
                    console.log('Using direct chat manager call');
                    window.chatManager.sendMessage();
                  }
                } catch (error) {
                  console.error('Error calling chatManager.sendMessage', error);
                }

                try {
                  // Method 2: Custom event
                  console.log('Dispatching sendMessage event');
                  const event = new CustomEvent('sendMessage', {
                    detail: { message: messageText }
                  });
                  document.dispatchEvent(event);
                } catch (error) {
                  console.error('Error dispatching sendMessage event', error);
                }

                try {
                  // Method 3: Simulate Enter key press
                  console.log('Simulating Enter key press');
                  const enterEvent = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true
                  });
                  userInput.dispatchEvent(enterEvent);
                } catch (error) {
                  console.error('Error simulating Enter key press', error);
                }

                // Method 4: Direct manipulation of chat UI as a last resort
                try {
                  if (!window.chatManager && document.getElementById('chatMessages')) {
                    console.log('Using direct UI manipulation as fallback');

                    // Add user message to chat
                    const chatMessages = document.getElementById('chatMessages');
                    const userMessageDiv = document.createElement('div');
                    userMessageDiv.className = 'message user-message';
                    userMessageDiv.innerHTML = `<div class="message-content">${messageText}</div>`;
                    chatMessages.appendChild(userMessageDiv);

                    // Clear input
                    userInput.value = '';

                    // Add AI response
                    const aiMessageDiv = document.createElement('div');
                    aiMessageDiv.className = 'message ai-message';
                    aiMessageDiv.innerHTML = `<div class="message-content">I'm sorry, but the chat functionality isn't fully initialized yet. Please try again in a moment.</div>`;
                    chatMessages.appendChild(aiMessageDiv);

                    // Scroll to bottom
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                  }
                } catch (error) {
                  console.error('Error with direct UI manipulation', error);
                }
              }

              // Return true to indicate we handled the event
              return true;
            }
            // Tab button handling
            else if (button.classList.contains('tab-btn')) {
              console.log('Tab button clicked in blank page sidebar:', button.id);

              // Remove active class from all tabs and contents
              document.querySelectorAll('.tab-btn').forEach(t => t.classList.remove('active'));
              document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

              // Add active class to clicked tab
              button.classList.add('active');

              // Show corresponding content
              const contentId = button.id.replace('Tab', 'Content');
              const content = document.getElementById(contentId);
              if (content) {
                content.classList.add('active');

                // Special handling for history tab
                if (button.id === 'historyTab' && window.historyManager) {
                  window.historyManager.loadHistoryList();
                }

                // Special handling for stats tab
                if (button.id === 'statsTab' && window.updateStatsDisplay) {
                  window.updateStatsDisplay();
                }
              }

              // Return true to indicate we handled the event
              return true;
            }
          }, true); // Use capture phase to ensure our handler runs first

          console.log('Added event handler to button:', button.id || button.className);
        } else {
          console.log('Button already initialized:', button.id || button.className);
        }
      });

      // Initialize provider selector
      const providerSelector = document.getElementById('providerSelector');
      if (providerSelector) {
        // Add a data attribute to track initialization
        if (!providerSelector.hasAttribute('data-initialized')) {
          providerSelector.setAttribute('data-initialized', 'true');

          // Force the selector to be visible and interactive
          providerSelector.style.pointerEvents = 'auto';
          providerSelector.style.zIndex = '9999';
          providerSelector.style.position = 'relative';
          providerSelector.style.display = 'block';
          providerSelector.style.visibility = 'visible';
          providerSelector.style.opacity = '1';

          // Add fresh event listener
          providerSelector.addEventListener('change', function() {
            console.log('Provider changed in blank page sidebar:', this.value);

            // Try to call the API manager directly
            if (window.apiManager && typeof window.apiManager.setProvider === 'function') {
              window.apiManager.setProvider(this.value);
            }

            // Also dispatch a custom event
            const event = new CustomEvent('providerChanged', {
              detail: { provider: this.value }
            });
            document.dispatchEvent(event);
          });

          console.log('Added event listener to providerSelector');
        } else {
          console.log('providerSelector already initialized');
        }
      } else {
        console.error('providerSelector element not found');
      }

      console.log('UI elements initialized for blank page sidebar');

      // Force focus on the input field after a delay
      setTimeout(() => {
        const userInput = document.getElementById('userInput');
        if (userInput) {
          userInput.focus();
        }

        // Make sure the chat tab is active
        const chatTab = document.getElementById('chatTab');
        if (chatTab) {
          chatTab.click();
        }
      }, 500);

      // Add a MutationObserver to watch for DOM changes and reinitialize if needed
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // Check if any new buttons were added
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === 1) { // Element node
                const newButtons = node.querySelectorAll ? node.querySelectorAll('button') : [];
                if (newButtons.length > 0) {
                  console.log(`Found ${newButtons.length} new buttons, reinitializing`);
                  initializeBlankPageUI();
                }
              }
            });
          }
        });
      });

      // Start observing the document with the configured parameters
      observer.observe(document.body, { childList: true, subtree: true });
      console.log('Added MutationObserver to watch for DOM changes');

      // Add a global flag to indicate initialization is complete
      window.blankPageUIInitialized = true;

    } catch (error) {
      console.error('Error initializing UI elements for blank page sidebar:', error);
    }
  };

  // Listen for messages from the parent window (for Brave sidebar)
  window.addEventListener('message', (event) => {
    console.log('Received message in popup:', event.data);

    // Handle sidebar closed message from Brave sidebar
    if (event.data && event.data.source === 'brave-sidebar') {
      if (event.data.action === 'sidebarClosed') {
        console.log('Received sidebarClosed message from Brave sidebar');
        // If we're in sidebar mode, close the window
        if (isSidebarMode) {
          console.log('In sidebar mode, closing window');
          setTimeout(() => window.close(), 100);
        }
      }
      else if (event.data.action === 'sidebarInitialized') {
        console.log('Received sidebarInitialized message from Brave sidebar');
        // Special handling for blank pages
        if (event.data.isBlankPage || isBlankPage) {
          console.log('Initializing blank page sidebar');

          // Add classes to indicate this is a blank page sidebar
          document.body.classList.add('blank-page-sidebar');
          document.body.classList.add('new-tab-sidebar');

          // Force global objects to be available in window scope
          if (chatManager) window.chatManager = chatManager;
          if (historyManager) window.historyManager = historyManager;
          if (exportManager) window.exportManager = exportManager;
          if (featureManager) window.featureManager = featureManager;
          if (uiManager) window.uiManager = uiManager;
          if (apiManager) window.apiManager = apiManager;

          // Make updateStatsDisplay available globally
          if (typeof updateStatsDisplay === 'function') {
            window.updateStatsDisplay = updateStatsDisplay;
          }

          // Use our comprehensive initialization function
          initializeBlankPageUI();

          // Schedule multiple initialization attempts to ensure everything works
          [100, 500, 1000, 2000].forEach(delay => {
            setTimeout(() => {
              console.log(`Scheduled initialization for blank page (delay: ${delay}ms)`);

              // Re-initialize UI elements
              initializeBlankPageUI();

              // Force focus on the input field
              const userInput = document.getElementById('userInput');
              if (userInput) {
                userInput.focus();
                console.log(`Focused input field (delay: ${delay}ms)`);
              }

              // Force the chat tab to be active
              const chatTab = document.getElementById('chatTab');
              if (chatTab) {
                chatTab.click();
                console.log(`Activated chat tab (delay: ${delay}ms)`);
              }
            }, delay);
          });

          // Respond to the parent window to confirm receipt
          try {
            event.source.postMessage({
              source: 'popup',
              action: 'messageReceived',
              originalAction: event.data.action,
              timestamp: new Date().getTime()
            }, '*');
            console.log('Sent confirmation message to parent window');
          } catch (error) {
            console.error('Error sending confirmation to parent window:', error);
          }
        }
      }
      else if (event.data.action === 'forceInitializeBlankPage') {
        console.log('Received forceInitializeBlankPage message from Brave sidebar', event.data);

        // Force global objects to be available in window scope
        if (chatManager) window.chatManager = chatManager;
        if (historyManager) window.historyManager = historyManager;
        if (exportManager) window.exportManager = exportManager;
        if (featureManager) window.featureManager = featureManager;
        if (uiManager) window.uiManager = uiManager;
        if (apiManager) window.apiManager = apiManager;
        // Don't expose storage manager directly to avoid hardcoded model issues

        // Make updateStatsDisplay available globally
        if (typeof updateStatsDisplay === 'function') {
          window.updateStatsDisplay = updateStatsDisplay;
        }

        // Add classes to indicate this is a blank page sidebar
        document.body.classList.add('blank-page-sidebar');
        document.body.classList.add('new-tab-sidebar');

        // Use our comprehensive initialization function
        initializeBlankPageUI();

        // Schedule multiple initialization attempts to ensure everything works
        [100, 500, 1000, 2000].forEach(delay => {
          setTimeout(() => {
            console.log(`Scheduled initialization for blank page (delay: ${delay}ms)`);

            // Re-initialize UI elements
            initializeBlankPageUI();

            // Force focus on the input field
            const userInput = document.getElementById('userInput');
            if (userInput) {
              userInput.focus();
              console.log(`Focused input field (delay: ${delay}ms)`);
            }

            // Force the chat tab to be active
            const chatTab = document.getElementById('chatTab');
            if (chatTab) {
              chatTab.click();
              console.log(`Activated chat tab (delay: ${delay}ms)`);
            }

            // Ensure all buttons have event handlers
            const allButtons = document.querySelectorAll('button');
            allButtons.forEach(button => {
              if (!button.hasAttribute('data-initialized')) {
                button.setAttribute('data-initialized', 'true');

                // Add a direct click handler
                button.addEventListener('click', function() {
                  console.log(`Button clicked (delay: ${delay}ms):`, button.id || button.className);
                });

                console.log(`Added event handler to button (delay: ${delay}ms):`, button.id || button.className);
              }
            });
          }, delay);
        });

        // Respond to the parent window to confirm receipt
        try {
          event.source.postMessage({
            source: 'popup',
            action: 'messageReceived',
            originalAction: event.data.action,
            timestamp: new Date().getTime()
          }, '*');
          console.log('Sent confirmation message to parent window');
        } catch (error) {
          console.error('Error sending confirmation to parent window:', error);
        }
      }
    }
  });

  // Tab navigation
  const tabs = document.querySelectorAll('.tab-btn');
  const tabContents = document.querySelectorAll('.tab-content');

  // Function to switch to a specific tab
  const switchToTab = (tabId) => {
    const tab = document.getElementById(tabId);
    if (!tab) return;

    tabs.forEach(t => t.classList.remove('active'));
    tabContents.forEach(content => content.classList.remove('active'));

    tab.classList.add('active');
    const contentId = tab.id.replace('Tab', 'Content');
    const contentElement = document.getElementById(contentId);
    if (contentElement) {
      contentElement.classList.add('active');
    }

    // Load history when history tab is clicked
    if (tab.id === 'historyTab') {
      historyManager.loadHistoryList();
    }

    // Update stats when stats tab is clicked
    if (tab.id === 'statsTab') {
      updateStatsDisplay();
    }

    // Special handling for settings tab - ensure user profile section is visible
    if (tab.id === 'settingsTab') {
      setTimeout(() => {
        const userProfileSection = document.getElementById('userProfileSection');
        if (userProfileSection) {
          userProfileSection.style.display = 'block';
          console.log('Settings tab clicked - ensuring user profile section is visible');

          // Update the user profile UI
          if (typeof dashboardConnector !== 'undefined' &&
              typeof dashboardConnector.updateUserProfileUI === 'function') {
            console.log('Settings tab clicked - updating user profile UI');
            console.log('Current user data:', dashboardConnector.userData);
            dashboardConnector.updateUserProfileUI();
          }
        }
      }, 100);
    }

    // Focus on input field when switching to chat tab
    if (tab.id === 'chatTab') {
      setTimeout(() => {
        const userInput = document.getElementById('userInput');
        if (userInput) {
          userInput.focus();
        }
      }, 100);
    }
  };

  // Add click event listeners to tabs
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      switchToTab(tab.id);
    });
  });

  // Add keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    // Only process keyboard shortcuts if not in an input field or textarea
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
      // Special case for Escape key to blur the input
      if (e.key === 'Escape') {
        e.target.blur();
        e.preventDefault();
      }
      return;
    }

    // Tab switching shortcuts (Alt+1, Alt+2, etc.)
    if (e.altKey && !e.ctrlKey && !e.shiftKey) {
      if (e.key === '1') {
        switchToTab('chatTab');
        e.preventDefault();
      } else if (e.key === '2') {
        switchToTab('historyTab');
        e.preventDefault();
      } else if (e.key === '3') {
        switchToTab('statsTab');
        e.preventDefault();
      } else if (e.key === '4') {
        switchToTab('settingsTab');
        e.preventDefault();
      }
    }

    // Focus on chat input (/)
    if (e.key === '/' && !e.ctrlKey && !e.altKey && !e.shiftKey) {
      const userInput = document.getElementById('userInput');
      if (userInput) {
        userInput.focus();
        e.preventDefault();
      }
    }

    // Clear chat (Alt+C)
    if (e.altKey && e.key === 'c') {
      const clearChatBtn = document.getElementById('clearChatBtn');
      if (clearChatBtn) {
        clearChatBtn.click();
        e.preventDefault();
      }
    }

    // Stop generation (Escape)
    if (e.key === 'Escape') {
      const stopBtn = document.getElementById('stopGenerationBtn');
      if (stopBtn && stopBtn.style.display !== 'none') {
        stopBtn.click();
        e.preventDefault();
      }
    }
  });

  // Load saved provider selection
  const providerSelector = document.getElementById('providerSelector');

  // First, clear any existing provider selection to ensure we start fresh
  await chrome.storage.local.remove('selectedProvider');
  console.log('Cleared existing provider from storage to start fresh');

  // Set the default provider to match the selected option in the dropdown
  const defaultProvider = 'direct-gemini/gemini-1.5-flash';
  await chrome.storage.local.set({ selectedProvider: defaultProvider });
  console.log('Set default provider to:', defaultProvider);

  // Verify it was set correctly
  const result = await chrome.storage.local.get('selectedProvider');
  const savedProvider = result.selectedProvider || defaultProvider;
  console.log('Loaded provider directly from storage:', savedProvider);

  // Check if the saved provider exists in the current options
  const providerExists = Array.from(providerSelector.options).some(option => option.value === savedProvider);

  if (providerExists) {
    providerSelector.value = savedProvider;
  } else {
    // If the saved provider doesn't exist, default to Gemini 1.5 Flash
    providerSelector.value = 'direct-gemini/gemini-1.5-flash';
    await chrome.storage.local.set({ selectedProvider: 'direct-gemini/gemini-1.5-flash' });
  }

  // No need to force set the provider anymore - use what the user selected

  console.log('Provider selector initialized with value:', providerSelector.value);

  // Add a class to highlight the selected option
  const highlightSelectedOption = () => {
    const selectedOption = providerSelector.options[providerSelector.selectedIndex];
    if (selectedOption) {
      selectedOption.classList.add('selected-option');
    }
  };

  highlightSelectedOption();

  providerSelector.addEventListener('change', async () => {
    const selectedProvider = providerSelector.value;
    console.log('Provider selector changed to:', selectedProvider);

    // Log the provider type for debugging
    if (selectedProvider.startsWith('direct-openai/')) {
      console.log('Selected an OpenAI direct model');
    } else if (selectedProvider.startsWith('direct-gemini/')) {
      console.log('Selected a Gemini direct model');
    } else if (selectedProvider.includes('/')) {
      console.log('Selected an OpenRouter model');
    }

    // Force clear any existing provider from storage first
    await chrome.storage.local.remove('selectedProvider');
    console.log('Cleared existing provider from storage');

    // Set the provider directly in storage to bypass any hardcoded values in the storage manager
    await chrome.storage.local.set({ selectedProvider: selectedProvider });
    console.log('Set new provider directly in storage:', selectedProvider);

    // Verify the provider was set correctly
    const result = await chrome.storage.local.get('selectedProvider');
    console.log('Verification after setting provider in popup:', result);

    // Double-check that the provider was set correctly
    if (result.selectedProvider !== selectedProvider) {
      console.error('Provider was not set correctly. Trying again...');
      // Try one more time with a direct storage call
      await chrome.storage.local.set({ selectedProvider: selectedProvider });

      // Verify again
      const secondCheck = await chrome.storage.local.get('selectedProvider');
      console.log('Second verification after setting provider:', secondCheck);
    }

    // Get the selected option text
    const selectedOption = providerSelector.options[providerSelector.selectedIndex];
    const selectedText = selectedOption.text;
    const optgroupLabel = selectedOption.parentElement.label;

    // Update the selected option highlighting
    Array.from(providerSelector.options).forEach(option => {
      option.classList.remove('selected-option');
    });
    highlightSelectedOption();

    // Extract the provider name from the model ID
    let providerName = '';
    if (selectedProvider.includes('/')) {
      providerName = selectedProvider.split('/')[0];
      // Capitalize first letter
      providerName = providerName.charAt(0).toUpperCase() + providerName.slice(1);
    }

    // All models require API keys now
    const apiKeys = await storage.getAPIKeys();

    // Check which API key is needed based on the provider
    let apiKeyNeeded = '';
    let apiKeyPresent = false;
    let fallbackKeyPresent = false;

    if (selectedProvider.startsWith('direct-openai/')) {
      apiKeyNeeded = 'OpenAI';
      apiKeyPresent = !!apiKeys.openai;
      fallbackKeyPresent = false; // No fallback for OpenAI - must use OpenAI API key

      // Extract the model name for logging
      const openaiModel = selectedProvider.replace('direct-openai/', '');
      console.log('OpenAI model selected:', openaiModel);
      console.log('OpenAI API key present:', apiKeyPresent);

    } else if (selectedProvider.startsWith('direct-gemini/')) {
      apiKeyNeeded = 'Gemini';
      apiKeyPresent = !!apiKeys.gemini;
      fallbackKeyPresent = false; // No fallback for Gemini - must use Gemini API key

      // Extract the model name for logging
      const geminiModel = selectedProvider.replace('direct-gemini/', '');
      console.log('Gemini model selected:', geminiModel);
      console.log('Gemini API key present:', apiKeyPresent);

    } else {
      apiKeyNeeded = 'OpenRouter';
      apiKeyPresent = !!apiKeys.openrouter;

      // Extract the provider and model parts for OpenRouter models
      if (selectedProvider.includes('/')) {
        const [provider, model] = selectedProvider.split('/');
        console.log('OpenRouter model selected:', selectedProvider);
        console.log('Provider:', provider);
        console.log('Model:', model);

        // Add specific logging for different providers
        if (provider === 'openai') {
          console.log('Using OpenAI model through OpenRouter:', model);
        } else if (provider === 'anthropic') {
          console.log('Using Anthropic model through OpenRouter:', model);
        } else if (provider === 'google') {
          console.log('Using Google model through OpenRouter:', model);
        } else if (provider === 'meta') {
          console.log('Using Meta model through OpenRouter:', model);
        } else if (provider === 'mistral') {
          console.log('Using Mistral model through OpenRouter:', model);
        }
      } else {
        console.log('OpenRouter model selected (invalid format):', selectedProvider);
        console.warn('Model should be in format "provider/model"');
      }

      console.log('OpenRouter API key present:', apiKeyPresent);
    }

    if (!apiKeyPresent) {
      if (fallbackKeyPresent && apiKeyNeeded !== 'OpenRouter') {
        // Show error message instead of fallback
        showStatusMessage(`${optgroupLabel} model selected: ${selectedText} - ${apiKeyNeeded} API key required in Settings`, true, 5000);

        // Show error message instead of fallback warning
        const chatMessages = document.getElementById('chatMessages');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'message ai error';
        errorDiv.innerHTML = `
          <div class="message-content">
            <p><strong>❌ API Key Required</strong></p>
            <p>You've selected a model (${selectedText}) but haven't provided a ${apiKeyNeeded} API key.</p>
            <p>Please add your ${apiKeyNeeded} API key in the settings to use this model.</p>
          </div>
        `;
        chatMessages.appendChild(errorDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
      } else {
        // Show a more prominent error
        showStatusMessage(`${optgroupLabel} model selected: ${selectedText} - ${apiKeyNeeded} API key required in Settings`, true, 5000);

        // Add a warning message to the chat
        const chatMessages = document.getElementById('chatMessages');
        const warningDiv = document.createElement('div');
        warningDiv.className = 'message ai error';
        warningDiv.innerHTML = `
          <div class="message-content">
            <p><strong>⚠️ API Key Required</strong></p>
            <p>You've selected a model (${selectedText}) but haven't provided a ${apiKeyNeeded} API key.</p>
            <p>Please go to the <strong>Settings tab</strong> and add your ${apiKeyNeeded} API key to use this model.</p>
          </div>
        `;
        chatMessages.appendChild(warningDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
      }
    } else {
      showStatusMessage(`${optgroupLabel}: ${selectedText} (${apiKeyNeeded} API key detected)`, false, 3000);
    }
  });

  // Toggle password visibility
  const toggleButtons = document.querySelectorAll('.toggle-visibility');
  toggleButtons.forEach(button => {
    button.addEventListener('click', () => {
      const input = button.previousElementSibling;
      const icon = button.querySelector('i');

      if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
      } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
      }
    });
  });

  // Dashboard connection input animation
  const dashboardToken = document.getElementById('dashboardToken');
  if (dashboardToken) {
    dashboardToken.addEventListener('focus', () => {
      const inputHighlight = document.querySelector('.input-highlight');
      if (inputHighlight) {
        inputHighlight.style.width = '100%';
      }
    });

    dashboardToken.addEventListener('blur', () => {
      const inputHighlight = document.querySelector('.input-highlight');
      if (inputHighlight) {
        inputHighlight.style.width = '0';
      }
    });
  }

  // Load API keys
  const apiKeys = await storage.getAPIKeys();

  const openrouterKeyElement = document.getElementById('openrouterKey');
  const openaiKeyElement = document.getElementById('openaiKey');
  const geminiKeyElement = document.getElementById('geminiKey');

  if (openrouterKeyElement) openrouterKeyElement.value = apiKeys.openrouter || '';
  if (openaiKeyElement) openaiKeyElement.value = apiKeys.openai || '';
  if (geminiKeyElement) geminiKeyElement.value = apiKeys.gemini || '';
  // Hugging Face API key handling has been removed as requested

  // Save API keys
  document.getElementById('saveKeys').addEventListener('click', async () => {
    const keys = {
      openrouter: document.getElementById('openrouterKey').value.trim(),
      openai: document.getElementById('openaiKey').value.trim(),
      gemini: document.getElementById('geminiKey').value.trim()
      // Hugging Face API key handling has been removed as requested
    };

    await storage.setAPIKeys(keys);
    showStatusMessage('API keys saved successfully');
  });

  // Load settings
  const settings = await storage.getSettings();
  document.getElementById('maxTokens').value = settings.maxTokens;
  document.getElementById('temperature').value = settings.temperature;
  document.getElementById('temperatureValue').textContent = settings.temperature;

  // Set new chat settings if they exist
  if (settings.hasOwnProperty('autoAnalyze')) {
    document.getElementById('autoAnalyze').checked = settings.autoAnalyze;
  }

  if (settings.hasOwnProperty('rememberHistory')) {
    document.getElementById('rememberHistory').checked = settings.rememberHistory;
  } else {
    // Default to true for remembering history
    document.getElementById('rememberHistory').checked = true;
  }

  // Sidebar mode is now always enabled by default
  await chrome.storage.local.set({ sidebarEnabled: true });

  // Update temperature display
  document.getElementById('temperature').addEventListener('input', function() {
    document.getElementById('temperatureValue').textContent = this.value;
  });

  // Save settings
  document.getElementById('saveSettings').addEventListener('click', async () => {
    const newSettings = {
      maxTokens: parseInt(document.getElementById('maxTokens').value),
      temperature: parseFloat(document.getElementById('temperature').value),
      autoAnalyze: document.getElementById('autoAnalyze').checked,
      rememberHistory: document.getElementById('rememberHistory').checked
    };

    await storage.setSettings(newSettings);

    showStatusMessage('Settings saved successfully');
  });

  // Dashboard connection
  const dashboardTokenInput = document.getElementById('dashboardToken');
  const connectDashboardBtn = document.getElementById('connectDashboard');
  const dashboardConnectionStatus = document.getElementById('dashboardConnectionStatus');

  // Update dashboard connection status UI
  function updateDashboardConnectionUI(connectionData) {
    const statusIndicator = dashboardConnectionStatus.querySelector('.status-indicator');
    const statusMessage = dashboardConnectionStatus.querySelector('.status-message');
    const dashboardTokenInput = document.getElementById('dashboardToken');
    const connectDashboardBtn = document.getElementById('connectDashboard');
    const userProfileSection = document.getElementById('userProfileSection');

    // Remove all status classes
    statusIndicator.classList.remove('connected', 'disconnected', 'connecting', 'error');

    if (connectionData.status === 'connected') {
      // Connected state
      statusIndicator.classList.add('connected');
      statusIndicator.innerHTML = '<i class="fas fa-check-circle"></i> Connected to dashboard';

      // Get the username from userData, log it for debugging
      const username = connectionData.userData?.username || 'Premium User';
      console.log('Username for dashboard connection UI:', username);
      console.log('Full userData object:', JSON.stringify(connectionData.userData));

      let statusText = `Your extension is synced with the Browzy AI dashboard as ${username}.`;

      // Show user profile section
      if (userProfileSection) {
        userProfileSection.style.display = 'block';
      }

      // Add plan information to status text if available
      if (connectionData.userPlan) {
        statusText += ` Current plan: Premium.`;

        // Add usage information to status text
        const requestsUsed = connectionData.userPlan.requestsUsed || 0;
        statusText += ` Usage: ${requestsUsed} requests (unlimited).`;
      }

      statusMessage.textContent = statusText;

      // Disable token input and change button to disconnect
      dashboardTokenInput.disabled = true;
      dashboardTokenInput.value = '••••••••••';
      connectDashboardBtn.innerHTML = '<i class="fas fa-unlink"></i>';
      connectDashboardBtn.title = 'Disconnect from Dashboard';

      // Update the user profile UI
      dashboardConnector.updateUserProfileUI();
    } else if (connectionData.status === 'connecting') {
      // Connecting state
      statusIndicator.classList.add('connecting');
      statusIndicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting...';
      statusMessage.textContent = 'Establishing connection to the Browzy AI dashboard...';

      // Disable input while connecting
      dashboardTokenInput.disabled = true;
      connectDashboardBtn.disabled = true;

      // Hide user profile section
      if (userProfileSection) {
        userProfileSection.style.display = 'none';
      }
    } else if (connectionData.status === 'error') {
      // Error state
      statusIndicator.classList.add('error');
      statusIndicator.innerHTML = '<i class="fas fa-exclamation-circle"></i> Connection failed';
      statusMessage.textContent = connectionData.error || 'Failed to connect to the dashboard. Please check your token and try again.';

      // Enable input for retry
      dashboardTokenInput.disabled = false;
      connectDashboardBtn.disabled = false;
      connectDashboardBtn.innerHTML = '<i class="fas fa-plug"></i>';
      connectDashboardBtn.title = 'Connect to Dashboard';

      // Hide user profile section
      if (userProfileSection) {
        userProfileSection.style.display = 'none';
      }
    } else {
      // Disconnected state
      statusIndicator.classList.add('disconnected');
      statusIndicator.innerHTML = '<i class="fas fa-times-circle"></i> Not connected to dashboard';
      statusMessage.textContent = 'Connect to the Browzy AI dashboard to sync your chat history and settings.';

      // Enable input
      dashboardTokenInput.disabled = false;
      connectDashboardBtn.disabled = false;
      connectDashboardBtn.innerHTML = '<i class="fas fa-plug"></i>';
      connectDashboardBtn.title = 'Connect to Dashboard';

      // Show user profile section with default values
      if (userProfileSection) {
        userProfileSection.style.display = 'block';

        // Update the user profile UI with default values
        dashboardConnector.updateUserProfileUI();
      }
    }
  }

  // Add connection listener
  dashboardConnector.addConnectionListener(updateDashboardConnectionUI);

  // Initialize user profile UI
  setTimeout(() => {
    console.log('Initializing user profile UI on popup load');
    console.log('Current user data:', dashboardConnector.userData);
    dashboardConnector.updateUserProfileUI();
  }, 500);

  // Test connection button
  document.getElementById('testConnection').addEventListener('click', async () => {
    try {
      showStatusMessage('Testing connection to server...', false);

      // Test the connection
      const success = await dashboardConnector.testConnection();

      if (success) {
        showStatusMessage('Connection to server successful!', false);

        // Show profile section with sample data for testing
        const userProfileSection = document.getElementById('userProfileSection');

        if (userProfileSection) {
          userProfileSection.style.display = 'block';
          console.log('Showing user profile section from test button');

          // Create temporary test data with a clearly visible username
          dashboardConnector.userData = {
            username: 'Test Username',
            userId: 'test-user-id'
          };
          console.log('Setting test username:', dashboardConnector.userData.username);

          dashboardConnector.userPlan = {
            id: 'premium',
            name: 'Premium',
            requestsUsed: 42,
            features: {
              maxRequestsPerDay: -1,
              smartSearch: true,
              multiTabSearch: true,
              exportHistory: true
            }
          };

          // Update the user profile UI with test data
          dashboardConnector.updateUserProfileUI();

          // Clear the test data after 5 seconds
          setTimeout(() => {
            if (!dashboardConnector.isConnectedToDashboard()) {
              dashboardConnector.userData = null;
              dashboardConnector.userPlan = null;
              dashboardConnector.updateUserProfileUI();
            }
          }, 5000);
        } else {
          console.log('userProfileSection element not found');
        }
      } else {
        showStatusMessage('Connection to server failed. Please check your network connection.', true);
      }
    } catch (error) {
      console.error('Error testing connection:', error);
      showStatusMessage('Error testing connection: ' + error.message, true);
    }
  });

  // Connect/disconnect dashboard button
  connectDashboardBtn.addEventListener('click', async () => {
    if (dashboardConnector.isConnectedToDashboard()) {
      // Disconnect if already connected
      await dashboardConnector.disconnect();
      showStatusMessage('Disconnected from Browzy AI dashboard', false);
    } else {
      // Connect with token
      const token = dashboardTokenInput.value.trim();

      if (!token) {
        showStatusMessage('Please enter a connection token', true);
        return;
      }

      // Try to connect with the token
      try {
        const connected = await dashboardConnector.connectWithToken(token);

        if (connected) {
          showStatusMessage('Connected to Browzy AI dashboard successfully', false);

          // Send chat history to dashboard after successful connection
          if (chatManager && chatManager.chatHistory && chatManager.chatHistory.length > 0) {
            try {
              console.log('Syncing chat history with dashboard...');
              await dashboardConnector.sendChatHistory(chatManager.chatHistory);
              console.log('Chat history synced with dashboard successfully');
            } catch (syncError) {
              console.error('Error syncing chat history with dashboard:', syncError);
            }
          }
        } else {
          showStatusMessage('Failed to connect to dashboard. Please check your token and try again.', true);
        }
      } catch (error) {
        console.error('Error connecting to dashboard:', error);
        showStatusMessage('Error connecting to dashboard: ' + error.message, true);
      }

      // Update UI to connecting state
      updateDashboardConnectionUI({ status: 'connecting' });

      // Try to connect
      const success = await dashboardConnector.connectWithToken(token);

      if (success) {
        showStatusMessage('Connected to Browzy AI dashboard successfully', false);

        // Send chat history to dashboard
        try {
          if (chatManager && chatManager.chatHistory && chatManager.chatHistory.length > 0) {
            // Format the chat history for sending
            const formattedHistory = chatManager.chatHistory.map(msg => ({
              id: msg.id || `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
              content: msg.content,
              sender: msg.sender,
              timestamp: msg.timestamp || new Date().toISOString(),
              url: msg.url || window.location.href,
              response: msg.sender === 'user' ? (
                // Find the AI response that follows this user message
                chatManager.chatHistory.find(m =>
                  m.sender === 'ai' &&
                  m.timestamp > msg.timestamp
                )?.content || ''
              ) : ''
            }));

            // Send only user messages with their responses
            const userMessages = formattedHistory.filter(msg => msg.sender === 'user');

            // Send in batches of 5 to avoid overwhelming the server
            for (let i = 0; i < userMessages.length; i += 5) {
              const batch = userMessages.slice(i, i + 5);
              await dashboardConnector.sendChatHistory(batch);
            }

            console.log(`Sent ${userMessages.length} messages to Browzy AI dashboard`);
          }
        } catch (error) {
          console.error('Error sending chat history to dashboard:', error);
        }

        // Fetch user plan information
        await dashboardConnector.fetchUserPlan();
      }
    }
  });



  // Load statistics
  await updateStatsDisplay();

  // Reset statistics
  document.getElementById('resetStats').addEventListener('click', async () => {
    if (confirm('Are you sure you want to reset all usage statistics?')) {
      await storage.resetStats();
      await updateStatsDisplay();

      // Also reset the model usage indicators
      document.querySelectorAll('.model-item').forEach(item => {
        item.classList.remove('has-usage', 'high-usage', 'medium-usage', 'low-usage');
        const usageElement = item.querySelector('.model-usage');
        if (usageElement) {
          usageElement.textContent = '0';
        }
      });

      // Reset model counts
      document.querySelectorAll('.model-count').forEach(count => {
        count.classList.remove('has-usage');
        const totalModels = count.closest('.model-column')?.querySelectorAll('.model-item').length || 0;
        count.textContent = totalModels.toString();
      });

      showStatusMessage('Statistics reset successfully');
    }
  });

  // Load chat history if enabled
  if (settings.rememberHistory) {
    await chatManager.loadHistory();
  }

  // Check for PDF text selection first
  try {
    // Check if the current tab is a PDF
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tab && tab.url && (
        tab.url.toLowerCase().endsWith('.pdf') ||
        tab.url.toLowerCase().includes('pdf') ||
        tab.url.toLowerCase().includes('viewer.html')
    )) {
      console.log('PDF detected, checking for selection...');

      // Try to silently get PDF selection
      await featureManager.handlePDFSelection(null, true);

      // Check if we got PDF data
      if (apiManager.hasScannedTabContent() && apiManager.getScannedTabContent().isPDF) {
        console.log('PDF text found and loaded automatically');
        // Actions dropdown button has been removed
      } else {
        // Actions dropdown button has been removed
      }
    }
  } catch (pdfError) {
    console.log('Error checking for PDF selection:', pdfError);
  }

  // Auto-analyze the page if enabled
  if (settings.autoAnalyze) {
    try {
      const pageContent = await apiManager.getPageContent();

      // Get page context type from background script
      const response = await chrome.runtime.sendMessage({ action: 'analyzePage' });

      let welcomeMessage = '';
      if (response && response.success) {
        const insights = response.insights;

        // Create context-aware welcome message
        if (insights.type === 'chess') {
          welcomeMessage = "I see you're viewing a chess page! I can analyze positions, suggest moves, and explain chess concepts.";

          // Automatically suggest a move if we have chess content
          if (pageContent.chess && (pageContent.chess.fen || pageContent.chess.pgn)) {
            try {
              // Call the chessHelper method to automatically suggest a move
              setTimeout(() => {
                featureManager.chessHelper();
              }, 1000); // Slight delay to ensure welcome message appears first
            } catch (chessError) {
              console.error('Error suggesting chess move:', chessError);
            }
          }
        } else if (insights.type === 'coding') {
          welcomeMessage = "I see you're on a coding/programming page! I can help explain algorithms, optimize code, and suggest solutions.";
        } else {
          // Add a simple analysis message
          const analysisPrompt = `
            Please provide a brief summary of this webpage:
            URL: ${pageContent.url}
            Title: ${pageContent.title}

            Content:
            ${chatManager.truncateContent(pageContent.content, 2000)}
          `;

          const aiResponse = await apiManager.sendRequest(analysisPrompt, {
            systemPrompt: "You are a webpage analysis assistant that provides concise, helpful summaries. Keep your response under 3 sentences."
          });

          welcomeMessage = `I've analyzed this page: ${aiResponse.text}`;
        }
      } else {
        welcomeMessage = "I can help you understand this webpage. Ask me anything about the content!";
      }

      // Replace existing AI message or add new one
      chatManager.addMessageToChat(welcomeMessage, 'ai');

      // Add a message about AI Agent Browser feature
      setTimeout(() => {
        chatManager.addMessageToChat(
          "🤖 **New AI Agent Browser Available!** Click the robot icon to access intelligent web browsing with AI-powered search, analysis, and comprehensive answers with source citations. Perfect for research, learning, and getting detailed information on any topic.",
          'ai'
        );
      }, 1000);
    } catch (error) {
      console.error('Auto-analysis error:', error);
    }
  }

  // Initialize file handler
  const fileHandler = new FileHandler(uiManager);

  // Initialize destress mode
  const destressMode = new DestressMode(uiManager);

  // Make file handler available to chat manager
  chatManager.fileHandler = fileHandler;

  // Setup sidebar toggle button
  const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
  if (sidebarToggleBtn) {
    sidebarToggleBtn.addEventListener('click', () => {
      console.log('Sidebar toggle button clicked');
      if (sidebarManager) {
        sidebarManager.toggleSidebar();
      }
    });
  }

  // Check for URL parameters - reuse the urlParams from earlier
  // const urlParams is already defined above
  const tabParam = urlParams.get('tab');
  const analyzeParam = urlParams.get('analyze');
  const sidebarParam = urlParams.get('sidebar');
  // We already have isBlankPage from earlier, so just use that
  // const blankPageParam = urlParams.get('blankPage');
  const retryParam = urlParams.get('retry');
  const directParam = urlParams.get('direct');
  // We already have isNewTab from earlier, so just use that
  // const newTabParam = urlParams.get('newTab');

  // Handle tab parameter
  if (tabParam) {
    console.log('Tab parameter detected:', tabParam);
    const tabButton = document.getElementById(`${tabParam}Tab`);
    if (tabButton) {
      tabButton.click();
    }
  }

  // Handle analyze parameter
  if (analyzeParam === 'true') {
    console.log('Analyze parameter detected, auto-analyzing page');
    setTimeout(() => {
      featureManager.analyzePageContent();
    }, 1000);
  }

  // Handle sidebar mode
  const isSidebarMode = sidebarParam === 'true';
  // Reuse isBlankPage and isNewTab from earlier
  // const isBlankPage = blankPageParam === 'true';
  const isRetry = retryParam === 'true';
  const isDirect = directParam === 'true';
  // const isNewTab = newTabParam === 'true';

  if (isSidebarMode) {
    console.log('Sidebar mode detected', { isBlankPage, isRetry, isDirect, isNewTab });
    document.body.classList.add('sidebar-mode');

    // Add special class for blank pages
    if (isBlankPage) {
      document.body.classList.add('blank-page-sidebar');
      console.log('Blank page sidebar mode detected');

      // Add special class for new tab pages
      if (isNewTab) {
        document.body.classList.add('new-tab-sidebar');
        console.log('New tab sidebar mode detected');
      }

      // For direct loading on blank pages, force immediate initialization
      if (isDirect || isNewTab) {
        console.log('Direct/NewTab loading detected, forcing immediate initialization');
        document.body.classList.add('direct-load');

        // Force all managers to be available in the global scope immediately
        setTimeout(() => {
          try {
            // Make all managers available globally
            if (chatManager) window.chatManager = chatManager;
            if (historyManager) window.historyManager = historyManager;
            if (exportManager) window.exportManager = exportManager;
            if (featureManager) window.featureManager = featureManager;
            if (uiManager) window.uiManager = uiManager;
            if (apiManager) window.apiManager = apiManager;
            // Don't expose storage manager directly to avoid hardcoded model issues

            console.log('Managers made available globally for direct loading');

            // Force initialization of UI elements
            initializeBlankPageUI();

            // For new tab pages, add additional initialization
            if (isNewTab) {
              console.log('Performing additional initialization for new tab page');

              // Force initialization again after a longer delay
              setTimeout(() => {
                initializeBlankPageUI();

                // Force focus on the input field
                const userInput = document.getElementById('userInput');
                if (userInput) {
                  userInput.focus();
                }

                // Force the chat tab to be active
                const chatTab = document.getElementById('chatTab');
                if (chatTab) {
                  chatTab.click();
                }
              }, 1000);
            }
          } catch (error) {
            console.error('Error in direct initialization:', error);
          }
        }, 100);
      }
    }

    // Force fixed width for Chrome Side Panel
    document.body.style.width = '400px';
    document.body.style.minWidth = '400px';
    document.body.style.maxWidth = '400px';

    // Apply fixed width to container
    const container = document.querySelector('.container');
    if (container) {
      container.style.width = '400px';
      container.style.minWidth = '400px';
      container.style.maxWidth = '400px';
      container.style.position = 'fixed';
      container.style.top = '0';
      container.style.right = '0';
      container.style.bottom = '0';
    }

    // Hide the sidebar toggle button in sidebar mode
    if (sidebarToggleBtn) {
      sidebarToggleBtn.style.display = 'none';
    }



    // Add a style tag with !important rules
    const styleTag = document.createElement('style');
    styleTag.textContent = `
      body.sidebar-mode {
        width: 400px !important;
        min-width: 400px !important;
        max-width: 400px !important;
        position: fixed !important;
        top: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
      }

      body.sidebar-mode .container {
        width: 400px !important;
        min-width: 400px !important;
        max-width: 400px !important;
      }
    `;
    document.head.appendChild(styleTag);

    console.log('UI adjusted for sidebar mode');

    // Special handling for sidebar mode on blank pages
    console.log('Running in sidebar mode, ensuring all UI elements are initialized');

    // Force initialization of UI elements
    setTimeout(() => {
      try {
        // Initialize chat input
        const userInput = document.getElementById('userInput');
        if (userInput) {
          userInput.focus();
          userInput.blur();

          // Make sure the input event listener is attached
          userInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
          });
        }

        // Initialize send button
        const sendButton = document.getElementById('sendMessage');
        if (sendButton) {
          sendButton.addEventListener('click', function() {
            const userInput = document.getElementById('userInput');
            if (userInput && userInput.value.trim()) {
              // Trigger a custom event that the chat manager can listen for
              const event = new CustomEvent('sendMessage', {
                detail: { message: userInput.value.trim() }
              });
              document.dispatchEvent(event);

              // Also try to call the chat manager directly
              if (window.chatManager && typeof window.chatManager.sendMessage === 'function') {
                window.chatManager.sendMessage();
              }
            }
          });
        }

        // Initialize provider selector
        const providerSelector = document.getElementById('providerSelector');
        if (providerSelector) {
          // Force a re-initialization of the change handler
          const oldProviderSelector = providerSelector.cloneNode(true);
          providerSelector.parentNode.replaceChild(oldProviderSelector, providerSelector);
        }

        console.log('UI elements initialized for sidebar mode');
      } catch (error) {
        console.error('Error initializing UI elements for sidebar mode:', error);
      }
    }, 500);
  }

  // Modern UI Toggle
  const toggleModernUI = (show) => {
    const regularChat = document.querySelector('.chat-messages');
    const regularChatInput = document.querySelector('.chat-input-container');
    const floatingChat = document.querySelector('.floating-chat-container');

    if (show) {
      regularChat.style.display = 'none';
      regularChatInput.style.display = 'none';
      floatingChat.style.display = 'flex';
      document.body.classList.add('modern-ui-active');

      // Set a welcome message for the AI agent browser
      const floatingChatMessages = document.querySelector('.floating-chat-messages');
      floatingChatMessages.innerHTML = `
        <div class="floating-chat-message">
          <div class="welcome-header">
            <i class="fas fa-robot"></i>
            <strong>AI Agent Browser Powered by Gemini</strong>
          </div>
          <p>I'm your intelligent browsing assistant! I can:</p>
          <ul>
            <li>🔍 <strong>Smart Search</strong> - Use advanced search techniques to find the best sources</li>
            <li>🧠 <strong>AI Analysis</strong> - Analyze content from multiple sources using Google Gemini</li>
            <li>📊 <strong>Comprehensive Answers</strong> - Synthesize information and provide detailed responses</li>
            <li>📚 <strong>Source Citations</strong> - Show you exactly where information comes from</li>
          </ul>
          <p><strong>Try asking:</strong> "What are the latest AI developments?" or "How does machine learning work?"</p>
          <div class="setup-note">
            <i class="fas fa-info-circle"></i>
            <small>For best results, add your Google Gemini API key in Settings</small>
          </div>
        </div>
      `;
    } else {
      regularChat.style.display = 'flex';
      regularChatInput.style.display = 'flex';
      floatingChat.style.display = 'none';
      document.body.classList.remove('modern-ui-active');
    }
  };

  // Don't show modern UI by default - let user toggle it manually
  // Initialize with regular chat visible
  toggleModernUI(false);

  // Handle floating chat close button
  document.querySelector('.floating-chat-close').addEventListener('click', () => {
    toggleModernUI(false);
    document.getElementById('toggleFloatingChat').classList.remove('active');
  });

  // Handle floating chat up button
  document.querySelector('.floating-chat-up-button').addEventListener('click', () => {
    toggleModernUI(false);
    document.getElementById('toggleFloatingChat').classList.remove('active');
  });

  // Handle floating chat toggle button
  document.getElementById('toggleFloatingChat').addEventListener('click', () => {
    const floatingChat = document.querySelector('.floating-chat-container');
    const isVisible = floatingChat.style.display === 'flex';

    if (isVisible) {
      floatingChat.style.display = 'none';
      document.getElementById('toggleFloatingChat').classList.remove('active');
    } else {
      floatingChat.style.display = 'flex';
      document.querySelector('.floating-chat-input').focus();
      document.getElementById('toggleFloatingChat').classList.add('active');

      // Set a welcome message for the AI agent browser if it's empty
      const floatingChatMessages = document.querySelector('.floating-chat-messages');
      if (!floatingChatMessages.innerHTML.trim()) {
        floatingChatMessages.innerHTML = `<div class="floating-chat-message">Welcome to AI Agent Browser! Ask me anything and I'll search the web, analyze multiple sources, and provide comprehensive answers with citations.</div>`;
      }
    }
  });

  // Setup floating actions dropdown
  const floatingActionsDropdownBtn = document.querySelector('.floating-actions-dropdown-btn');
  const floatingActionsDropdownMenu = document.querySelector('.floating-actions-dropdown-menu');

  // Toggle dropdown when clicking the button
  if (floatingActionsDropdownBtn && floatingActionsDropdownMenu) {
    floatingActionsDropdownBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      floatingActionsDropdownMenu.classList.toggle('show');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (floatingActionsDropdownBtn && floatingActionsDropdownMenu &&
          !floatingActionsDropdownBtn.contains(e.target) &&
          !floatingActionsDropdownMenu.contains(e.target)) {
        floatingActionsDropdownMenu.classList.remove('show');
      }
    });
  }

  // Floating menu buttons are now handled by setupFloatingMenuItem below



  // Close dropdown when clicking outside - already handled by the event listener above

  // Setup floating menu items - using try/catch to handle potential missing elements
  try {
    // Define the setupFloatingMenuItem function with proper error handling
    const setupFloatingMenuItem = (id, action) => {
      try {
        const element = document.getElementById(id);
        if (element && floatingActionsDropdownMenu) {
          element.addEventListener('click', () => {
            try {
              action();
              floatingActionsDropdownMenu.classList.remove('show');
            } catch (actionError) {
              console.error(`Error executing action for ${id}:`, actionError);
            }
          });
        }
      } catch (elementError) {
        console.error(`Error setting up element ${id}:`, elementError);
      }
    };

    // Setup all floating menu items
    if (featureManager && exportManager) {
      setupFloatingMenuItem('floatingTranslateButton', () => translateUIManager.showTranslateDialog());
      setupFloatingMenuItem('floatingScanTabButton', () => featureManager.scanOtherTab());
      setupFloatingMenuItem('floatingExportChatButton', () => exportManager.exportChatToText());

      setupFloatingMenuItem('floatingWebMcpButton', () => {
        // Show the Coming Soon popup instead of initializing Web MCP
        const comingSoonPopup = document.getElementById('comingSoonPopup');
        if (comingSoonPopup) {
          comingSoonPopup.style.display = 'flex';
          // Add animation class if needed
          setTimeout(() => {
            comingSoonPopup.classList.add('active');
          }, 10);
        }
      });

      setupFloatingMenuItem('floatingVideoAnalysisButton', () => featureManager.analyzeVideo());

      setupFloatingMenuItem('floatingWebsiteAnalyzerButton', () => featureManager.analyzeWebsite());

      setupFloatingMenuItem('floatingPdfToolsButton', () => {
        if (!window.pdfDialog) {
          window.pdfDialog = new PDFDialog(featureManager);
        }
        window.pdfDialog.showDialog();
      });

      setupFloatingMenuItem('floatingVideoAnalysisButton', () => featureManager.analyzeVideo());

      setupFloatingMenuItem('floatingCreativeStudioButton', () => {
        if (!window.creativeStudioDialog) {
          window.creativeStudioDialog = new CreativeStudioDialog(featureManager);
        }
        window.creativeStudioDialog.showDialog();
      });


    } else {
      console.error('featureManager or exportManager not initialized yet');
    }
  } catch (error) {
    console.error('Error setting up floating menu items:', error);
  }



  // We're now using the handleWebSearch function for all web search functionality

  // We no longer need to observe the regular chat messages since the floating chat is now dedicated to web searches
  // and doesn't need to display AI responses from the main chat

  // Setup file upload functionality
  const fileUploadBtn = document.getElementById('fileUploadBtn');
  const fileUploadInput = document.getElementById('fileUpload');
  const filePreview = document.getElementById('filePreview');
  const fileName = document.getElementById('fileName');
  const fileSource = document.getElementById('fileSource');
  const removeFileBtn = document.getElementById('removeFile');

  // File source popup elements
  const fileSourcePopup = document.getElementById('fileSourcePopup');
  const closeFileSourcePopup = document.getElementById('closeFileSourcePopup');
  const localFileOption = document.getElementById('localFileOption');
  const destressOption = document.getElementById('destressOption');



  // Add click event for file upload button - show coming soon popup
  fileUploadBtn.addEventListener('click', () => {
    // Show the Coming Soon popup instead of opening file dialog
    const uploadComingSoonPopup = document.getElementById('uploadComingSoonPopup');
    if (uploadComingSoonPopup) {
      uploadComingSoonPopup.style.display = 'flex';
      // Add animation class if needed
      setTimeout(() => {
        uploadComingSoonPopup.classList.add('active');
      }, 10);
    }
  });

  // Add click event for destress button
  const destressBtn = document.getElementById('destressBtn');
  destressBtn.addEventListener('click', () => {
    destressMode.showDestressDialog();
    showStatusMessage('Destress mode activated. Take a moment to relax.', false, 3000);
  });

  // Web search button has been removed from the UI

  // Add click event for floating chat close button
  const floatingChatClose = document.querySelector('.floating-chat-close');
  floatingChatClose.addEventListener('click', () => {
    const floatingChatContainer = document.querySelector('.floating-chat-container');
    floatingChatContainer.style.display = 'none';
  });

  // Add click event for floating chat up button
  const floatingChatUpButton = document.querySelector('.floating-chat-up-button');
  floatingChatUpButton.addEventListener('click', () => {
    const floatingChatMessages = document.querySelector('.floating-chat-messages');
    floatingChatMessages.scrollTop = 0;
  });

  // Add event listener for floating chat input
  document.querySelector('.floating-chat-input').addEventListener('keydown', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      const query = e.target.value.trim();
      if (query) {
        handleWebSearch(query);
        e.target.value = '';
      }
    }
  });



  // Function to handle AI agent web search
  async function handleWebSearch(query) {
    const floatingChatMessages = document.querySelector('.floating-chat-messages');

    // Add user message
    const userMessageDiv = document.createElement('div');
    userMessageDiv.className = 'floating-chat-message user';
    userMessageDiv.textContent = query;
    floatingChatMessages.appendChild(userMessageDiv);

    // Scroll to bottom
    floatingChatMessages.scrollTop = floatingChatMessages.scrollHeight;

    // Detect if this should use AI agent or simple search
    const shouldUseAIAgent = detectAIAgentNeed(query);

    if (!shouldUseAIAgent) {
      console.log('Using simple search for basic query:', query);
      await handleSimpleWebSearch(query, floatingChatMessages);
      return;
    }

    console.log('Using AI Agent for complex query:', query);

    // Create AI Agent Browser instance if it doesn't exist
    if (!window.aiAgentBrowser) {
      window.aiAgentBrowser = new AIAgentBrowser(apiManager);
    }

    // Create progress indicator
    const progressDiv = document.createElement('div');
    progressDiv.className = 'floating-chat-message ai-progress';
    progressDiv.innerHTML = `
      <div class="ai-agent-progress">
        <div class="progress-header">
          <i class="fas fa-robot"></i>
          <span class="progress-title">AI Agent Browsing</span>
        </div>
        <div class="progress-stage">Initializing...</div>
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
        <div class="progress-details"></div>
      </div>
    `;
    floatingChatMessages.appendChild(progressDiv);
    floatingChatMessages.scrollTop = floatingChatMessages.scrollHeight;

    // Progress callback to update UI
    const onProgress = (progress) => {
      const stageElement = progressDiv.querySelector('.progress-stage');
      const detailsElement = progressDiv.querySelector('.progress-details');
      const progressFill = progressDiv.querySelector('.progress-fill');

      stageElement.textContent = progress.message || 'Processing...';

      // Update progress bar based on stage
      const stageProgress = {
        'analyzing': 10,
        'planning': 25,
        'searching': 50,
        'fetching': 75,
        'synthesizing': 90,
        'complete': 100
      };

      const progressPercent = stageProgress[progress.stage] || 0;
      progressFill.style.width = `${progressPercent}%`;

      if (progress.stage === 'complete') {
        setTimeout(() => {
          progressDiv.remove();
        }, 1000);
      }
    };

    try {
      console.log('AI Agent Browser - Starting search for:', query);

      // Check if AI Agent Browser is available
      if (!window.AIAgentBrowser) {
        throw new Error('AI Agent Browser class not loaded');
      }

      // Use AI Agent Browser for intelligent search
      const result = await window.aiAgentBrowser.browse(query, onProgress);

      if (result.success) {
        // Display comprehensive AI response
        displayAIAgentResponse(result, floatingChatMessages);

        // Log success for debugging
        console.log('✅ AI Agent Browser completed successfully:', {
          query: result.query,
          sources: result.sources?.length || 0,
          testMode: result.testMode || false
        });
      } else {
        // Fallback to simple search if AI agent fails
        console.warn('AI Agent failed, falling back to simple search:', result.error);
        progressDiv.remove();
        await handleSimpleWebSearch(query, floatingChatMessages);
      }

    } catch (error) {
      console.error('AI Agent Browser error:', error);

      // Remove progress indicator
      if (progressDiv && progressDiv.parentNode) {
        progressDiv.remove();
      }

      // Show detailed error message
      const errorDiv = document.createElement('div');
      errorDiv.className = 'floating-chat-message ai error';
      errorDiv.innerHTML = `
        <div class="error-message">
          <i class="fas fa-exclamation-triangle"></i>
          <div>
            <strong>AI Agent Error:</strong> ${error.message || 'Unknown error occurred'}
            <br><small>Falling back to simple search...</small>
          </div>
        </div>
      `;
      floatingChatMessages.appendChild(errorDiv);
      floatingChatMessages.scrollTop = floatingChatMessages.scrollHeight;

      // Fallback to simple search
      try {
        await handleSimpleWebSearch(query, floatingChatMessages);
      } catch (fallbackError) {
        console.error('Fallback search also failed:', fallbackError);

        // Show final error message
        const finalErrorDiv = document.createElement('div');
        finalErrorDiv.className = 'floating-chat-message ai error';
        finalErrorDiv.innerHTML = `
          <div class="error-message">
            <i class="fas fa-times-circle"></i>
            <span>Both AI Agent and fallback search failed. Please check your internet connection and try again.</span>
          </div>
        `;
        floatingChatMessages.appendChild(finalErrorDiv);
        floatingChatMessages.scrollTop = floatingChatMessages.scrollHeight;
      }
    }
  }

  // Fallback simple web search function
  async function handleSimpleWebSearch(query, floatingChatMessages) {
    // Create an instance of the EnhancedWebsiteNLPManager if it doesn't exist
    if (!window.smartSearchNLP) {
      window.smartSearchNLP = new EnhancedWebsiteNLPManager(apiManager);
    }

    // Log the original query for debugging
    console.log('Simple Search - Original query:', query);

    // Process the query using the enhanced NLP manager
    const searchResult = window.smartSearchNLP.processQuery(query);
    const searchUrl = searchResult.url;
    const platform = searchResult.platform;
    const processedQuery = searchResult.processedQuery;

    // Log the processed query for debugging
    console.log('Simple Search - Processed query:', processedQuery);
    console.log('Simple Search - Platform:', platform);
    console.log('Simple Search - URL:', searchUrl);

    // Continue with the rest of the simple search logic...
    displaySimpleSearchResult(searchResult, floatingChatMessages);
  }

  // Global function to show search details (called from AI agent response)
  window.showSearchDetails = function(sessionId) {
    if (window.aiAgentBrowser) {
      const session = window.aiAgentBrowser.getCurrentSession();
      if (session && session.id === sessionId) {
        console.log('🔍 Search Session Details:', session);

        // Create a detailed view popup
        const detailsPopup = document.createElement('div');
        detailsPopup.className = 'search-details-popup';
        detailsPopup.innerHTML = `
          <div class="search-details-content">
            <div class="search-details-header">
              <h3>Search Session Details</h3>
              <button class="close-details-btn" onclick="this.parentElement.parentElement.parentElement.remove()">×</button>
            </div>
            <div class="search-details-body">
              <div class="detail-section">
                <h4>Query Analysis</h4>
                <pre>${JSON.stringify(session.steps.find(s => s.step === 'query_analysis')?.data || {}, null, 2)}</pre>
              </div>
              <div class="detail-section">
                <h4>Search Queries</h4>
                <pre>${JSON.stringify(session.steps.find(s => s.step === 'search_queries')?.data || [], null, 2)}</pre>
              </div>
              <div class="detail-section">
                <h4>Search Results</h4>
                <pre>${JSON.stringify(session.steps.find(s => s.step === 'search_results')?.data || [], null, 2)}</pre>
              </div>
            </div>
          </div>
        `;

        document.body.appendChild(detailsPopup);
      }
    }
  };

  // Add keyboard shortcut for AI Agent Browser
  document.addEventListener('keydown', (e) => {
    // Alt + S for AI Agent Browser
    if (e.altKey && e.key === 's') {
      e.preventDefault();
      const toggleButton = document.getElementById('toggleFloatingChat');
      if (toggleButton) {
        toggleButton.click();
      }
    }
  });

  // Debug function for testing AI Agent Browser
  window.testAIAgent = async function(query = "What is artificial intelligence?") {
    console.log('🧪 Testing AI Agent Browser...');

    try {
      if (!window.AIAgentBrowser) {
        console.error('❌ AI Agent Browser class not loaded');
        return;
      }

      if (!window.aiAgentBrowser) {
        console.log('Creating new AI Agent Browser instance...');
        window.aiAgentBrowser = new AIAgentBrowser(apiManager);
      }

      console.log(`🔍 Testing query: "${query}"`);

      const result = await window.aiAgentBrowser.browse(query, (progress) => {
        console.log(`📊 Progress: ${progress.stage} - ${progress.message}`);
      });

      if (result.success) {
        console.log('✅ Test completed successfully!');
        console.log('📊 Results:', {
          query: result.query,
          intent: result.analysis?.intent,
          sourcesFound: result.sources?.length || 0,
          confidence: result.answer?.confidence || 0,
          testMode: result.testMode || false
        });

        if (result.testMode) {
          console.log('ℹ️ Running in test mode - configure API keys for full functionality');
        }
      } else {
        console.log('❌ Test failed:', result.error);
      }

      return result;
    } catch (error) {
      console.error('❌ Test error:', error);
      return { success: false, error: error.message };
    }
  };

  // Function to detect if query needs AI agent or simple search
  function detectAIAgentNeed(query) {
    const lowerQuery = query.toLowerCase().trim();

    // Simple queries that don't need AI agent
    const simplePatterns = [
      /^(youtube|instagram|twitter|facebook|tiktok|pinterest)\s/,
      /\s(youtube|instagram|twitter|facebook|tiktok|pinterest)$/,
      /^search\s+(.+)\s+on\s+(youtube|instagram|twitter|facebook|tiktok|pinterest)/,
      /^find\s+(.+)\s+on\s+(youtube|instagram|twitter|facebook|tiktok|pinterest)/,
      /^(play|watch|listen)\s/,
      /^(download|get)\s/
    ];

    // Check if it's a simple platform-specific search
    for (const pattern of simplePatterns) {
      if (pattern.test(lowerQuery)) {
        return false;
      }
    }

    // Complex queries that benefit from AI agent
    const complexPatterns = [
      /^(what|how|why|when|where|which|who)\s/,
      /^(explain|describe|analyze|compare|contrast)\s/,
      /^(latest|recent|current|new)\s/,
      /(research|study|analysis|report|paper)/,
      /(vs|versus|compared to|difference between)/,
      /(best|top|recommended|popular)/,
      /(learn|tutorial|guide|steps)/,
      /(pros and cons|advantages|disadvantages)/,
      /(trends|developments|updates|news)/
    ];

    // Check if it's a complex query
    for (const pattern of complexPatterns) {
      if (pattern.test(lowerQuery)) {
        return true;
      }
    }

    // Default: use AI agent for queries longer than 4 words
    const wordCount = lowerQuery.split(/\s+/).length;
    return wordCount > 4;
  }

  // Auto-test on load if in development mode
  if (window.location.hostname === 'localhost' || window.location.protocol === 'chrome-extension:') {
    setTimeout(() => {
      console.log('🚀 AI Agent Browser loaded! Test with: testAIAgent("your question here")');
    }, 1000);
  }

  // Function to display AI Agent response
  function displayAIAgentResponse(result, floatingChatMessages) {
    const responseDiv = document.createElement('div');
    responseDiv.className = 'floating-chat-message ai ai-agent-response';

    const answer = result.answer;
    const sources = result.sources || [];

    responseDiv.innerHTML = `
      <div class="ai-agent-header">
        <i class="fas fa-robot"></i>
        <span class="ai-agent-title">AI Agent Response</span>
        <span class="gemini-powered">Powered by Gemini</span>
        <span class="confidence-badge">Confidence: ${Math.round((answer.confidence || 0.8) * 100)}%</span>
      </div>

      <div class="ai-agent-answer">
        <h4>Answer:</h4>
        <p>${answer.answer || 'No comprehensive answer available.'}</p>
      </div>

      ${answer.keyFindings && answer.keyFindings.length > 0 ? `
        <div class="ai-agent-findings">
          <h4>Key Findings:</h4>
          <ul>
            ${answer.keyFindings.map(finding => `<li>${finding}</li>`).join('')}
          </ul>
        </div>
      ` : ''}

      <div class="ai-agent-sources">
        <h4>Sources (${sources.length}):</h4>
        <div class="sources-list">
          ${sources.slice(0, 5).map((source, index) => `
            <div class="source-item">
              <div class="source-header">
                <a href="${source.url}" target="_blank" class="source-title">
                  ${source.title}
                </a>
                <span class="source-reliability">${Math.round((source.reliability || 0.5) * 100)}% reliable</span>
              </div>
              <div class="source-hostname">${source.hostname}</div>
              <div class="source-snippet">${source.snippet || 'No description available'}</div>
            </div>
          `).join('')}
        </div>
      </div>

      ${answer.followUpQuestions && answer.followUpQuestions.length > 0 ? `
        <div class="ai-agent-followup">
          <h4>Follow-up Questions:</h4>
          <div class="followup-questions">
            ${answer.followUpQuestions.map(question => `
              <button class="followup-question" onclick="handleWebSearch('${question}')">${question}</button>
            `).join('')}
          </div>
        </div>
      ` : ''}

      ${answer.limitations ? `
        <div class="ai-agent-limitations">
          <h4>Limitations:</h4>
          <p>${answer.limitations}</p>
        </div>
      ` : ''}

      <div class="ai-agent-footer">
        <span class="search-time">Search completed in ${Date.now() - new Date(result.timestamp).getTime()}ms</span>
        <button class="view-details-btn" onclick="showSearchDetails('${result.sessionId}')">
          <i class="fas fa-info-circle"></i> View Details
        </button>
      </div>
    `;

    floatingChatMessages.appendChild(responseDiv);
    floatingChatMessages.scrollTop = floatingChatMessages.scrollHeight;
  }

  // Function to display simple search result (fallback)
  function displaySimpleSearchResult(searchResult, floatingChatMessages) {
    const searchUrl = searchResult.url;
    const platform = searchResult.platform;
    const processedQuery = searchResult.processedQuery;

    // Add feedback mechanism for improving the model
    // Store the query and result for future training
    if (!window.searchQueryHistory) {
      window.searchQueryHistory = [];
    }

    // Add to history with timestamp
    window.searchQueryHistory.push({
      originalQuery: query,
      processedQuery: processedQuery,
      platform: platform,
      timestamp: new Date().toISOString()
    });

    // Limit history size
    if (window.searchQueryHistory.length > 100) {
      window.searchQueryHistory.shift();
    }

    // Save to local storage for persistence
    try {
      chrome.storage.local.set({
        'searchQueryHistory': window.searchQueryHistory
      });
    } catch (error) {
      console.error('Error saving search query history:', error);
    }

    // Get a user-friendly platform name
    const platformNames = {
      youtube: 'YouTube',
      instagram: 'Instagram',
      twitter: 'Twitter/X',
      pinterest: 'Pinterest',
      facebook: 'Facebook',
      linkedin: 'LinkedIn',
      reddit: 'Reddit',
      tiktok: 'TikTok',
      github: 'GitHub',
      stackoverflow: 'Stack Overflow',
      amazon: 'Amazon',
      ebay: 'eBay',
      flipkart: 'Flipkart',
      spotify: 'Spotify',
      netflix: 'Netflix',
      primevideo: 'Prime Video',
      hotstar: 'Hotstar',
      browse: 'Website',
      direct: 'Website',
      google: 'Google'
    };

    // Get platform icons
    const platformIcons = {
      youtube: 'fa-youtube',
      instagram: 'fa-instagram',
      twitter: 'fa-twitter',
      pinterest: 'fa-pinterest',
      facebook: 'fa-facebook',
      linkedin: 'fa-linkedin',
      reddit: 'fa-reddit',
      tiktok: 'fa-tiktok',
      github: 'fa-github',
      stackoverflow: 'fa-stack-overflow',
      amazon: 'fa-amazon',
      ebay: 'fa-shopping-cart',
      flipkart: 'fa-shopping-bag',
      spotify: 'fa-spotify',
      netflix: 'fa-film',
      primevideo: 'fa-video',
      hotstar: 'fa-tv',
      browse: 'fa-globe',
      direct: 'fa-globe',
      google: 'fa-search'
    };

    const platformName = platformNames[platform] || 'Search';
    const platformIcon = platformIcons[platform] ? `fa-brands ${platformIcons[platform]}` : 'fas fa-search';

    // Add AI response message with enhanced information
    const aiMessageDiv = document.createElement('div');
    aiMessageDiv.className = 'floating-chat-message';

    // Get confidence level for UI feedback
    const confidence = searchResult.confidence || 0.5;
    let confidenceDisplay = '';

    if (confidence > 0.8) {
      confidenceDisplay = '<span class="confidence high">High confidence</span>';
    } else if (confidence > 0.6) {
      confidenceDisplay = '<span class="confidence medium">Medium confidence</span>';
    } else {
      confidenceDisplay = '<span class="confidence low">Best guess</span>';
    }

    if (platform === 'direct' || platform === 'browse') {
      aiMessageDiv.innerHTML = `
        <div class="search-result">
          <div class="search-platform"><i class="${platformIcon}"></i> ${platformName} ${confidenceDisplay}</div>
          <div class="search-query">Opening: ${processedQuery || searchUrl}</div>
          <div class="search-action"><a href="${searchUrl}" target="_blank">Open website</a></div>
        </div>
      `;
    } else if (searchResult.isConversational) {
      // For conversational queries, show more detailed response
      let understanding = processedQuery ?
        `I understand you want to find: <strong>${processedQuery}</strong>` :
        `I'll take you to ${platformName}`;

      aiMessageDiv.innerHTML = `
        <div class="search-result">
          <div class="search-platform"><i class="${platformIcon}"></i> ${platformName} ${confidenceDisplay}</div>
          <div class="search-understanding">${understanding}</div>
          <div class="search-action"><a href="${searchUrl}" target="_blank">Open ${platformName} results</a></div>
        </div>
      `;
    } else {
      aiMessageDiv.innerHTML = `
        <div class="search-result">
          <div class="search-platform"><i class="${platformIcon}"></i> ${platformName} ${confidenceDisplay}</div>
          <div class="search-query">Searching for: "${processedQuery}"</div>
          <div class="search-action"><a href="${searchUrl}" target="_blank">Open search results</a></div>
        </div>
      `;
    }

    floatingChatMessages.appendChild(aiMessageDiv);

    // Scroll to bottom
    floatingChatMessages.scrollTop = floatingChatMessages.scrollHeight;

    // Open the URL in a new tab
    chrome.tabs.create({ url: searchUrl });

    // Log the search for analytics
    console.log('Smart Search:', {
      originalQuery: query,
      processedQuery: processedQuery,
      platform: platform,
      url: searchUrl
    });
  }

  // Close file source popup
  closeFileSourcePopup.addEventListener('click', () => {
    fileSourcePopup.style.display = 'none';
  });

  // Open Destress Mode
  destressOption.addEventListener('click', () => {
    fileSourcePopup.style.display = 'none';
    destressMode.showDestressDialog();
    showStatusMessage('Destress mode activated. Take a moment to relax.', false, 3000);
  });



  // Close popups when clicking outside
  fileSourcePopup.addEventListener('click', (event) => {
    if (event.target === fileSourcePopup) {
      fileSourcePopup.style.display = 'none';
    }
  });



  // Handle local file option - show coming soon popup
  localFileOption.addEventListener('click', () => {
    fileSourcePopup.style.display = 'none';
    // Show the Coming Soon popup instead of opening file dialog
    const uploadComingSoonPopup = document.getElementById('uploadComingSoonPopup');
    if (uploadComingSoonPopup) {
      uploadComingSoonPopup.style.display = 'flex';
      // Add animation class if needed
      setTimeout(() => {
        uploadComingSoonPopup.classList.add('active');
      }, 10);
    }
  });



  // Handle local file selection
  fileUploadInput.addEventListener('change', async (event) => {
    if (event.target.files.length > 0) {
      const file = event.target.files[0];

      // Process the file
      const success = await fileHandler.processFile(file);

      if (success) {
        // Show file preview with local icon
        fileSource.innerHTML = '<i class="fas fa-laptop"></i>';
        fileName.textContent = file.name;
        filePreview.style.display = 'block';
      }
    }
  });

  // Remove file when clicking the remove button
  removeFileBtn.addEventListener('click', () => {
    fileHandler.clearFile();
    fileUploadInput.value = '';
    filePreview.style.display = 'none';
  });

  // Setup chat message sending
  const sendButton = document.getElementById('sendMessage');
  const userInput = document.getElementById('userInput');

  // Auto-resize textarea function
  function autoResizeTextarea(textarea) {
    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = 'auto';
    // Set the height to match content (with a max height)
    const newHeight = Math.min(textarea.scrollHeight, 120);
    textarea.style.height = newHeight + 'px';
  }

  // Initialize textarea height
  autoResizeTextarea(userInput);

  // Add input event listener for auto-resize
  userInput.addEventListener('input', () => {
    autoResizeTextarea(userInput);
  });

  sendButton.addEventListener('click', () => {
    chatManager.sendMessage();
    // Reset textarea height after sending
    setTimeout(() => autoResizeTextarea(userInput), 0);
  });

  userInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      chatManager.sendMessage();
      // Reset textarea height after sending
      setTimeout(() => autoResizeTextarea(userInput), 0);
    }
  });

  // Setup actions dropdown
  const actionsDropdownBtn = document.getElementById('actionsDropdownBtn');
  const actionsDropdownMenu = document.getElementById('actionsDropdownMenu');

  // Toggle dropdown when clicking the button
  actionsDropdownBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    actionsDropdownMenu.classList.toggle('show');
  });

  // Close dropdown when clicking outside
  document.addEventListener('click', (e) => {
    if (!actionsDropdownBtn.contains(e.target) && !actionsDropdownMenu.contains(e.target)) {
      actionsDropdownMenu.classList.remove('show');
    }
  });

  // Initialize the translate UI manager and make it globally available
  window.translateUIManager = new TranslateUIManager(uiManager, featureManager);

  // Setup translate button
  document.getElementById('translateButton').addEventListener('click', () => {
    window.translateUIManager.showTranslateDialog();
    actionsDropdownMenu.classList.remove('show');
  });

  // Setup scan tab button
  const scanTabButton = document.getElementById('scanTabButton');
  scanTabButton.addEventListener('click', () => {
    featureManager.scanOtherTab();
    actionsDropdownMenu.classList.remove('show');
  });

  // Setup Export Chat button
  document.getElementById('exportChatButton').addEventListener('click', () => {
    exportManager.exportChatToText();
    actionsDropdownMenu.classList.remove('show');
  });



  // Setup Web MCP button with Coming Soon popup
  document.getElementById('webMcpButton').addEventListener('click', () => {
    // Show the Coming Soon popup instead of initializing Web MCP
    const comingSoonPopup = document.getElementById('comingSoonPopup');
    if (comingSoonPopup) {
      comingSoonPopup.style.display = 'flex';
      // Add animation class if needed
      setTimeout(() => {
        comingSoonPopup.classList.add('active');
      }, 10);
    }
    actionsDropdownMenu.classList.remove('show');
  });

  // Setup Saved Links button
  document.getElementById('savedLinksButton').addEventListener('click', () => {
    // Initialize the Saved Links Manager if not already initialized
    if (!window.savedLinksManager) {
      window.savedLinksManager = new SavedLinksManager(uiManager);
    }
    window.savedLinksManager.showDialog();
    actionsDropdownMenu.classList.remove('show');
  });

  // Setup Website Analyzer button
  document.getElementById('websiteAnalyzerButton').addEventListener('click', () => {
    featureManager.analyzeWebsite();
    actionsDropdownMenu.classList.remove('show');
  });

  // Setup PDF Tools button
  document.getElementById('pdfToolsButton').addEventListener('click', () => {
    // Initialize the PDF Dialog if not already initialized
    if (!window.pdfDialog) {
      window.pdfDialog = new PDFDialog(featureManager);
    }
    window.pdfDialog.showDialog();
    actionsDropdownMenu.classList.remove('show');
  });

  // Setup Video Analysis button
  document.getElementById('videoAnalysisButton').addEventListener('click', () => {
    featureManager.analyzeVideo();
    actionsDropdownMenu.classList.remove('show');
  });


  // Setup Creative Studio button
  document.getElementById('creativeStudioButton').addEventListener('click', () => {
    // Initialize the Creative Studio Dialog if not already initialized
    if (!window.creativeStudioDialog) {
      window.creativeStudioDialog = new CreativeStudioDialog(featureManager);
    }
    window.creativeStudioDialog.showDialog();
    actionsDropdownMenu.classList.remove('show');
  });


  // Setup clear chat button
  document.getElementById('clearChat').addEventListener('click', () => {
    chatManager.clearChat();
  });

  // Setup close sidebar button
  const closeSidebarBtn = document.getElementById('closeSidebarBtn');
  if (closeSidebarBtn) {
    closeSidebarBtn.addEventListener('click', () => {
      console.log('Close sidebar button clicked');
      if (sidebarManager) {
        sidebarManager.closeSidebar();
      } else {
        console.error('SidebarManager not initialized');
        // Fallback: try to close the window anyway
        window.close();
      }
    });
  }





  // Check if we have scanned tab content and update the button state
  if (apiManager.hasScannedTabContent()) {
    scanTabButton.classList.add('active');
    const tabInfo = apiManager.getSelectedTabInfo();
    scanTabButton.title = tabInfo ?
      `Currently scanning: ${tabInfo.title} (click to clear)` :
      'Clear scanned tab content';

    // Update the dropdown button to show an indicator
    actionsDropdownBtn.classList.add('has-active');
  }

  // Listen for PDF text available events
  chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
    if (message.action === 'pdfTextAvailable') {
      console.log('PDF text available event received:', message);

      // Show PDF indicator on the actions dropdown button
      actionsDropdownBtn.classList.add('has-active');

      // Try to silently load the PDF text
      featureManager.handlePDFSelection(null, true);

      // Send response to acknowledge receipt
      sendResponse({ success: true });
    }
    return true; // Keep the message channel open for async response
  });

  // Listen for changes in scanned tab content
  document.addEventListener('scannedContentChanged', (event) => {
    if (event.detail.hasContent) {
      scanTabButton.classList.add('active');
      const tabInfo = event.detail.tabInfo || apiManager.getSelectedTabInfo();
      scanTabButton.title = tabInfo ?
        `Currently scanning: ${tabInfo.title} (click to clear)` :
        'Clear scanned tab content';

      // Update the dropdown button to show an indicator
      actionsDropdownBtn.classList.add('has-active');
    } else {
      scanTabButton.classList.remove('active');
      scanTabButton.title = 'Scan another tab';

      // Remove indicator from dropdown button
      actionsDropdownBtn.classList.remove('has-active');
    }
  });

  // Setup suggestion chips
  const suggestionChips = document.querySelectorAll('.suggestion-chip');
  suggestionChips.forEach(chip => {
    chip.addEventListener('click', () => {
      const chipText = chip.textContent.trim();

      // Handle special feature chips
      if (chipText.includes('Share analysis')) {
        featureManager.shareAnalysis();
        return;
      } else if (chipText.includes('Use shared analysis')) {
        featureManager.useSharedAnalysis();
        return;
      }

      // For other suggestion chips, use the standard chat flow
      userInput.value = chipText;
      chatManager.sendMessage();
    });
  });

  // Window unload - save chat history
  window.addEventListener('beforeunload', () => {
    // Save chat history if enabled
    if (settings.rememberHistory && chatManager.chatHistory.length > 1) {
      chatManager.saveHistory();
    }
  });

  // Also save history when a message is sent
  const originalSendMessage = chatManager.sendMessage;
  chatManager.sendMessage = async function() {
    await originalSendMessage.apply(this, arguments);
    if (settings.rememberHistory && this.chatHistory.length > 1) {
      this.saveHistory();
    }
  };

  // Helper function to show status messages
  function showStatusMessage(message, isError = false, duration = 3000) {
    const statusElement = document.getElementById('statusMessage');

    // Clear previous content
    statusElement.innerHTML = '';

    // Add icon based on message type
    const icon = document.createElement('i');
    if (isError) {
      icon.className = 'fas fa-exclamation-circle';
      icon.style.color = 'var(--danger-color)';
    } else {
      icon.className = 'fas fa-check-circle';
      icon.style.color = 'var(--success-color)';
    }

    // Add icon and message
    statusElement.appendChild(icon);
    statusElement.appendChild(document.createTextNode(' ' + message));

    // Auto-clear message after specified duration
    setTimeout(() => {
      statusElement.innerHTML = '<i class="fas fa-info-circle"></i> Ready to help';
    }, duration);
  }

  // Helper function to update stats display
  async function updateStatsDisplay() {
    const stats = await storage.getStats();
    console.log('Current stats:', stats);

    // Initialize stats if they don't exist
    if (!stats.openrouter) {
      stats.openrouter = { requests: 0, tokens: 0 };
    }
    if (!stats['direct-openai']) {
      stats['direct-openai'] = { requests: 0, tokens: 0 };
    }
    if (!stats['direct-gemini']) {
      stats['direct-gemini'] = { requests: 0, tokens: 0 };
    }
    if (!stats.gemini) {
      stats.gemini = { requests: 0, tokens: 0 };
    }
    if (!stats.anthropic) {
      stats.anthropic = { requests: 0, tokens: 0 };
    }
    if (!stats.mistral) {
      stats.mistral = { requests: 0, tokens: 0 };
    }
    if (!stats.cohere) {
      stats.cohere = { requests: 0, tokens: 0 };
    }

    // Combine stats for providers that might have multiple entries
    const geminiRequests = (stats['direct-gemini']?.requests || 0) + (stats.gemini?.requests || 0);
    const geminiTokens = (stats['direct-gemini']?.tokens || 0) + (stats.gemini?.tokens || 0);

    // Calculate totals from all providers
    const totalRequests =
      (stats.openrouter?.requests || 0) +
      (stats['direct-openai']?.requests || 0) +
      geminiRequests +
      (stats.anthropic?.requests || 0) +
      (stats.mistral?.requests || 0) +
      (stats.cohere?.requests || 0);

    const totalTokens =
      (stats.openrouter?.tokens || 0) +
      (stats['direct-openai']?.tokens || 0) +
      geminiTokens +
      (stats.anthropic?.tokens || 0) +
      (stats.mistral?.tokens || 0) +
      (stats.cohere?.tokens || 0);

    // Update summary stats
    document.getElementById('totalRequests').textContent = totalRequests.toLocaleString();
    document.getElementById('totalTokens').textContent = totalTokens.toLocaleString();

    // Calculate average response time (if available)
    let avgResponseTime = "1.2s"; // Default fallback
    if (stats.responseTimes && stats.responseTimes.length > 0) {
      const sum = stats.responseTimes.reduce((a, b) => a + b, 0);
      const avg = sum / stats.responseTimes.length;
      avgResponseTime = (avg / 1000).toFixed(1) + "s"; // Convert to seconds with 1 decimal place
    }
    document.getElementById('avgResponseTime').textContent = avgResponseTime;

    // Update provider stats
    document.getElementById('openrouterRequests').textContent = stats.openrouter.requests.toLocaleString();
    document.getElementById('openrouterTokens').textContent = stats.openrouter.tokens.toLocaleString();

    // Update OpenAI stats
    document.getElementById('openaiRequests').textContent = stats['direct-openai'].requests.toLocaleString();
    document.getElementById('openaiTokens').textContent = stats['direct-openai'].tokens.toLocaleString();

    // Update Gemini stats
    document.getElementById('geminiRequests').textContent = geminiRequests.toLocaleString();
    document.getElementById('geminiTokens').textContent = geminiTokens.toLocaleString();

    // Update model usage stats if available
    updateModelUsageStats(stats);
  }

  // Helper function to update model usage stats
  function updateModelUsageStats(stats) {
    console.log('Updating model usage stats based on:', stats);

    try {
      // Get all model items in the DOM
      const modelItems = document.querySelectorAll('.model-item');

      // Create a map to track model usage
      const modelUsage = {};

      // Get the available models from the provider selector
      const providerSelector = document.getElementById('providerSelector');
      const availableModels = Array.from(providerSelector.options).map(option => option.text);
      console.log('Available models in dropdown:', availableModels);

      // Extract model usage data from stats
      // For now, we'll use a more accurate distribution based on the available models

      // Process direct OpenAI models
      if (stats['direct-openai'] && stats['direct-openai'].requests > 0) {
        // Get the actual OpenAI models from the dropdown
        const openaiModels = availableModels.filter(model =>
          model.includes('GPT-4') || model.includes('GPT-3.5')
        );

        // If we have model-specific stats, use them
        if (stats['direct-openai'].modelStats) {
          Object.keys(stats['direct-openai'].modelStats).forEach(model => {
            modelUsage[model] = stats['direct-openai'].modelStats[model];
          });
        } else {
          // Otherwise distribute evenly among available models
          const totalOpenAIRequests = stats['direct-openai'].requests;
          const perModelRequests = Math.round(totalOpenAIRequests / openaiModels.length);

          openaiModels.forEach(model => {
            modelUsage[model] = perModelRequests;
          });
        }
      }

      // Process Gemini models
      if ((stats.gemini && stats.gemini.requests > 0) ||
          (stats['direct-gemini'] && stats['direct-gemini'].requests > 0)) {

        const totalGeminiRequests = (stats.gemini?.requests || 0) + (stats['direct-gemini']?.requests || 0);

        // Get the actual Gemini models from the dropdown
        const geminiModels = availableModels.filter(model => model.includes('Gemini'));

        // If we have model-specific stats, use them
        if ((stats.gemini && stats.gemini.modelStats) ||
            (stats['direct-gemini'] && stats['direct-gemini'].modelStats)) {

          const combinedModelStats = {
            ...(stats.gemini?.modelStats || {}),
            ...(stats['direct-gemini']?.modelStats || {})
          };

          Object.keys(combinedModelStats).forEach(model => {
            modelUsage[model] = combinedModelStats[model];
          });
        } else {
          // Otherwise distribute evenly among available models
          const perModelRequests = Math.round(totalGeminiRequests / Math.max(geminiModels.length, 1));

          geminiModels.forEach(model => {
            modelUsage[model] = perModelRequests;
          });
        }
      }

      // Process OpenRouter models
      if (stats.openrouter && stats.openrouter.requests > 0) {
        // Get the actual OpenRouter models from the dropdown
        const openRouterModels = availableModels.filter(model =>
          model.includes('Claude') ||
          model.includes('Mistral') ||
          model.includes('Command') ||
          (model.includes('GPT') && !model.includes('GPT-4o'))
        );

        // If we have model-specific stats, use them
        if (stats.openrouter.modelStats) {
          Object.keys(stats.openrouter.modelStats).forEach(model => {
            modelUsage[model] = stats.openrouter.modelStats[model];
          });
        } else {
          // Otherwise distribute evenly among available models
          const totalORRequests = stats.openrouter.requests;
          const perModelRequests = Math.round(totalORRequests / Math.max(openRouterModels.length, 1));

          openRouterModels.forEach(model => {
            modelUsage[model] = perModelRequests;
          });
        }
      }

      // Process specific provider stats if available
      ['anthropic', 'mistral', 'cohere'].forEach(provider => {
        if (stats[provider] && stats[provider].requests > 0) {
          // Get models for this provider
          const providerModels = availableModels.filter(model => {
            if (provider === 'anthropic') return model.includes('Claude');
            if (provider === 'mistral') return model.includes('Mistral');
            if (provider === 'cohere') return model.includes('Command');
            return false;
          });

          // If we have model-specific stats, use them
          if (stats[provider].modelStats) {
            Object.keys(stats[provider].modelStats).forEach(model => {
              modelUsage[model] = stats[provider].modelStats[model];
            });
          } else {
            // Otherwise distribute evenly
            const totalRequests = stats[provider].requests;
            const perModelRequests = Math.round(totalRequests / Math.max(providerModels.length, 1));

            providerModels.forEach(model => {
              modelUsage[model] = perModelRequests;
            });
          }
        }
      });

      // Update the DOM with model usage data
      modelItems.forEach(item => {
        const modelNameElement = item.querySelector('.model-name');
        const modelUsageElement = item.querySelector('.model-usage');

        if (modelNameElement && modelUsageElement) {
          const modelName = modelNameElement.textContent;
          const usage = modelUsage[modelName] || 0;

          // Update the usage count
          modelUsageElement.textContent = usage.toLocaleString();

          // Add a visual indicator for usage
          if (usage > 0) {
            item.classList.add('has-usage');

            // Add different classes based on usage level
            if (usage > 100) {
              item.classList.add('high-usage');
              item.classList.remove('medium-usage', 'low-usage');
            } else if (usage > 50) {
              item.classList.add('medium-usage');
              item.classList.remove('high-usage', 'low-usage');
            } else {
              item.classList.add('low-usage');
              item.classList.remove('high-usage', 'medium-usage');
            }
          } else {
            item.classList.remove('has-usage', 'high-usage', 'medium-usage', 'low-usage');
          }
        }
      });

      // Update model counts in column headers
      updateModelCounts();

      // Update cost estimation based on actual usage
      updateCostEstimation(stats);

    } catch (error) {
      console.error('Error updating model usage stats:', error);
    }
  }

  // Helper function to update model counts in column headers
  function updateModelCounts() {
    try {
      const modelColumns = document.querySelectorAll('.model-column');

      modelColumns.forEach(column => {
        const modelList = column.querySelector('.model-list');
        const modelCountElement = column.querySelector('.model-count');

        if (modelList && modelCountElement) {
          // Count models with usage > 0
          const usedModels = modelList.querySelectorAll('.model-item.has-usage').length;
          const totalModels = modelList.querySelectorAll('.model-item').length;

          // Update the count display
          modelCountElement.textContent = usedModels > 0 ? `${usedModels}/${totalModels}` : totalModels;

          // Add a visual indicator if any models are used
          if (usedModels > 0) {
            modelCountElement.classList.add('has-usage');
          } else {
            modelCountElement.classList.remove('has-usage');
          }
        }
      });
    } catch (error) {
      console.error('Error updating model counts:', error);
    }
  }

  // Helper function to update cost estimation
  function updateCostEstimation(stats) {
    try {
      // Define cost per 1000 tokens for different providers
      const costPerThousandTokens = {
        'openai': {
          'gpt-4o': 0.01,
          'gpt-4-turbo': 0.01,
          'gpt-3.5-turbo': 0.0015,
          'default': 0.01 // Default for any OpenAI model
        },
        'anthropic': {
          'claude-3-opus': 0.015,
          'claude-3-sonnet': 0.008,
          'claude-3-haiku': 0.0025,
          'default': 0.01 // Default for any Anthropic model
        },
        'gemini': {
          'gemini-1.5-pro': 0.0035,
          'gemini-1.5-flash': 0.0015,
          'default': 0.0025 // Default for any Gemini model
        },
        'mistral': {
          'mistral-large': 0.008,
          'mistral-medium': 0.003,
          'mistral-small': 0.0015,
          'default': 0.004 // Default for any Mistral model
        },
        'cohere': {
          'command-r-plus': 0.003,
          'command-r': 0.0015,
          'default': 0.002 // Default for any Cohere model
        },
        'default': 0.005 // Default for any other provider
      };

      // Calculate costs for each provider
      let openaiCost = 0;
      let anthropicCost = 0;
      let geminiCost = 0;

      // Calculate OpenAI cost
      if (stats['direct-openai'] && stats['direct-openai'].tokens > 0) {
        const tokens = stats['direct-openai'].tokens;
        // If we have model-specific stats, use them
        if (stats['direct-openai'].modelStats) {
          Object.keys(stats['direct-openai'].modelStats).forEach(model => {
            const modelTokens = stats['direct-openai'].modelTokens?.[model] || 0;
            const modelCost = costPerThousandTokens.openai[model.toLowerCase()] || costPerThousandTokens.openai.default;
            openaiCost += (modelTokens / 1000) * modelCost;
          });
        } else {
          // Otherwise use the default cost
          openaiCost = (tokens / 1000) * costPerThousandTokens.openai.default;
        }
      }

      // Calculate Anthropic cost
      if (stats.anthropic && stats.anthropic.tokens > 0) {
        const tokens = stats.anthropic.tokens;
        // If we have model-specific stats, use them
        if (stats.anthropic.modelStats) {
          Object.keys(stats.anthropic.modelStats).forEach(model => {
            const modelTokens = stats.anthropic.modelTokens?.[model] || 0;
            const modelCost = costPerThousandTokens.anthropic[model.toLowerCase()] || costPerThousandTokens.anthropic.default;
            anthropicCost += (modelTokens / 1000) * modelCost;
          });
        } else {
          // Otherwise use the default cost
          anthropicCost = (tokens / 1000) * costPerThousandTokens.anthropic.default;
        }
      }

      // Calculate Gemini cost
      if ((stats.gemini && stats.gemini.tokens > 0) || (stats['direct-gemini'] && stats['direct-gemini'].tokens > 0)) {
        const tokens = (stats.gemini?.tokens || 0) + (stats['direct-gemini']?.tokens || 0);
        // If we have model-specific stats, use them
        const combinedModelStats = {
          ...(stats.gemini?.modelStats || {}),
          ...(stats['direct-gemini']?.modelStats || {})
        };

        if (Object.keys(combinedModelStats).length > 0) {
          const combinedModelTokens = {
            ...(stats.gemini?.modelTokens || {}),
            ...(stats['direct-gemini']?.modelTokens || {})
          };

          Object.keys(combinedModelStats).forEach(model => {
            const modelTokens = combinedModelTokens[model] || 0;
            const modelCost = costPerThousandTokens.gemini[model.toLowerCase()] || costPerThousandTokens.gemini.default;
            geminiCost += (modelTokens / 1000) * modelCost;
          });
        } else {
          // Otherwise use the default cost
          geminiCost = (tokens / 1000) * costPerThousandTokens.gemini.default;
        }
      }

      // Update the cost cards
      document.querySelector('.cost-card:nth-child(1) .cost-value').textContent = '$' + openaiCost.toFixed(2);
      document.querySelector('.cost-card:nth-child(2) .cost-value').textContent = '$' + geminiCost.toFixed(2);
      document.querySelector('.cost-card:nth-child(3) .cost-value').textContent = '$' + anthropicCost.toFixed(2);

      // Update total cost
      const totalCost = openaiCost + geminiCost + anthropicCost;
      document.querySelector('.total-cost-value').textContent = '$' + totalCost.toFixed(2);

    } catch (error) {
      console.error('Error updating cost estimation:', error);
    }
  }

  // Persistent toggle menu has been removed

  // Load search query history for NLP training
  loadSearchQueryHistory();

  // Setup Coming Soon popup close button
  const closeComingSoonBtn = document.getElementById('closeComingSoonPopup');
  if (closeComingSoonBtn) {
    closeComingSoonBtn.addEventListener('click', () => {
      const comingSoonPopup = document.getElementById('comingSoonPopup');
      if (comingSoonPopup) {
        comingSoonPopup.style.display = 'none';
        comingSoonPopup.classList.remove('active');
      }
    });
  }

  // Setup Coming Soon popup Notify Me button
  const notifyMeButton = document.getElementById('notifyMeButton');
  if (notifyMeButton) {
    notifyMeButton.addEventListener('click', () => {
      // Show a notification that the user will be notified
      if (uiManager) {
        uiManager.showStatus('You will be notified when Web MCP is available! 🚀', false, 3000);
      }

      // Close the popup
      const comingSoonPopup = document.getElementById('comingSoonPopup');
      if (comingSoonPopup) {
        comingSoonPopup.style.display = 'none';
        comingSoonPopup.classList.remove('active');
      }
    });
  }

  // Close Coming Soon popup when clicking outside
  const comingSoonPopup = document.getElementById('comingSoonPopup');
  if (comingSoonPopup) {
    comingSoonPopup.addEventListener('click', (e) => {
      if (e.target === comingSoonPopup) {
        comingSoonPopup.style.display = 'none';
        comingSoonPopup.classList.remove('active');
      }
    });
  }

  // Setup Upload Coming Soon popup close button
  const closeUploadComingSoonBtn = document.getElementById('closeUploadComingSoonPopup');
  if (closeUploadComingSoonBtn) {
    closeUploadComingSoonBtn.addEventListener('click', () => {
      const uploadComingSoonPopup = document.getElementById('uploadComingSoonPopup');
      if (uploadComingSoonPopup) {
        uploadComingSoonPopup.style.display = 'none';
        uploadComingSoonPopup.classList.remove('active');
      }
    });
  }

  // Setup Upload Coming Soon popup Notify Me button
  const notifyMeUploadButton = document.getElementById('notifyMeUploadButton');
  if (notifyMeUploadButton) {
    notifyMeUploadButton.addEventListener('click', () => {
      // Show a notification that the user will be notified
      if (uiManager) {
        uiManager.showStatus('You will be notified when Upload from Computer is available! 📁', false, 3000);
      }

      // Close the popup
      const uploadComingSoonPopup = document.getElementById('uploadComingSoonPopup');
      if (uploadComingSoonPopup) {
        uploadComingSoonPopup.style.display = 'none';
        uploadComingSoonPopup.classList.remove('active');
      }
    });
  }

  // Close Upload Coming Soon popup when clicking outside
  const uploadComingSoonPopup = document.getElementById('uploadComingSoonPopup');
  if (uploadComingSoonPopup) {
    uploadComingSoonPopup.addEventListener('click', (e) => {
      if (e.target === uploadComingSoonPopup) {
        uploadComingSoonPopup.style.display = 'none';
        uploadComingSoonPopup.classList.remove('active');
      }
    });
  }
});

// Load search query history from storage
function loadSearchQueryHistory() {
  try {
    chrome.storage.local.get('searchQueryHistory', (result) => {
      if (result.searchQueryHistory) {
        window.searchQueryHistory = result.searchQueryHistory;
        console.log(`Loaded ${window.searchQueryHistory.length} search queries from history for NLP training`);
      } else {
        window.searchQueryHistory = [];
        console.log('No search query history found, starting fresh');
      }
    });
  } catch (error) {
    console.error('Error loading search query history:', error);
    window.searchQueryHistory = [];
  }
}

// Persistent toggle menu function has been removed

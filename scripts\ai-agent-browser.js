'use strict';

/**
 * AI Agent Browser - Perplexity Comet-like browsing agent
 * Provides intelligent web browsing with LLM-powered query understanding,
 * advanced Google search techniques, and comprehensive answer generation
 */
class AIAgentBrowser {
  constructor(apiManager) {
    this.apiManager = apiManager;
    this.searchHistory = [];
    this.currentSession = null;
    this.testMode = !apiManager || !apiManager.validateApiKey; // Enable test mode if no API manager
    
    // Advanced search operators for Google dorking
    this.searchOperators = {
      site: 'site:',
      filetype: 'filetype:',
      intitle: 'intitle:',
      inurl: 'inurl:',
      intext: 'intext:',
      related: 'related:',
      cache: 'cache:',
      define: 'define:',
      stocks: 'stocks:',
      weather: 'weather:',
      movie: 'movie:',
      map: 'map:',
      book: 'book:',
      info: 'info:',
      link: 'link:',
      daterange: '',
      before: 'before:',
      after: 'after:'
    };

    // Query intent patterns for better understanding
    this.intentPatterns = {
      definition: /^(what is|define|meaning of|explain)/i,
      comparison: /^(compare|difference between|vs|versus)/i,
      howTo: /^(how to|how do|steps to)/i,
      news: /^(news about|latest news|recent news)/i,
      academic: /^(research|study|paper|academic)/i,
      shopping: /^(buy|purchase|price|cost|shop)/i,
      local: /^(near me|nearby|local|in my area)/i,
      weather: /^(weather|temperature|forecast)/i,
      stocks: /^(stock price|share price|market)/i,
      technical: /^(error|fix|troubleshoot|debug)/i
    };

    // Source reliability scoring
    this.sourceReliability = {
      'wikipedia.org': 0.9,
      'stackoverflow.com': 0.9,
      'github.com': 0.8,
      'reddit.com': 0.7,
      'medium.com': 0.7,
      'quora.com': 0.6,
      'youtube.com': 0.6,
      'twitter.com': 0.5,
      'facebook.com': 0.4
    };
  }

  /**
   * Main entry point for AI agent browsing
   * @param {string} query - User's search query
   * @param {Function} onProgress - Progress callback function
   * @returns {Promise<Object>} - Comprehensive search results with AI analysis
   */
  async browse(query, onProgress = () => {}) {
    try {
      onProgress({ stage: 'analyzing', message: 'Understanding your query...' });

      // Create new session
      this.currentSession = {
        id: Date.now().toString(),
        query: query,
        startTime: new Date(),
        steps: []
      };

      // Test mode for immediate feedback
      if (this.testMode) {
        return await this.runTestMode(query, onProgress);
      }

      // Step 1: Analyze query with LLM
      const queryAnalysis = await this.analyzeQueryWithLLM(query);
      this.currentSession.steps.push({ step: 'query_analysis', data: queryAnalysis });
      
      onProgress({ 
        stage: 'planning', 
        message: `Detected intent: ${queryAnalysis.intent}. Planning search strategy...` 
      });

      // Step 2: Generate optimized search queries
      const searchQueries = await this.generateSearchQueries(queryAnalysis);
      this.currentSession.steps.push({ step: 'search_queries', data: searchQueries });

      onProgress({ 
        stage: 'searching', 
        message: `Searching with ${searchQueries.length} optimized queries...` 
      });

      // Step 3: Perform searches and collect results
      const searchResults = await this.performAdvancedSearches(searchQueries, onProgress);
      this.currentSession.steps.push({ step: 'search_results', data: searchResults });

      onProgress({ 
        stage: 'fetching', 
        message: `Found ${searchResults.length} results. Analyzing content...` 
      });

      // Step 4: Fetch and analyze top results
      const analyzedContent = await this.fetchAndAnalyzeContent(searchResults, onProgress);
      this.currentSession.steps.push({ step: 'content_analysis', data: analyzedContent });

      onProgress({ 
        stage: 'synthesizing', 
        message: 'Synthesizing information and generating response...' 
      });

      // Step 5: Generate comprehensive answer
      const finalAnswer = await this.generateComprehensiveAnswer(
        queryAnalysis, 
        analyzedContent, 
        query
      );
      this.currentSession.steps.push({ step: 'final_answer', data: finalAnswer });

      // Store session in history
      this.searchHistory.push(this.currentSession);

      onProgress({ 
        stage: 'complete', 
        message: 'Search complete!' 
      });

      return {
        success: true,
        sessionId: this.currentSession.id,
        query: query,
        analysis: queryAnalysis,
        sources: analyzedContent,
        answer: finalAnswer,
        searchQueries: searchQueries,
        timestamp: new Date()
      };

    } catch (error) {
      console.error('AI Agent Browser error:', error);
      return {
        success: false,
        error: error.message,
        query: query,
        timestamp: new Date()
      };
    }
  }

  /**
   * Analyze user query using LLM to understand intent and extract parameters
   * @param {string} query - User's search query
   * @returns {Promise<Object>} - Query analysis results
   */
  async analyzeQueryWithLLM(query) {
    try {
      // Check if API manager is available and has a valid API key
      if (!this.apiManager || !this.apiManager.validateApiKey) {
        console.warn('API manager not available, using fallback analysis');
        return this.fallbackQueryAnalysis(query);
      }

      const analysisPrompt = `
Analyze this search query and provide a structured response:

Query: "${query}"

Please analyze and respond with a JSON object containing:
{
  "intent": "definition|comparison|howTo|news|academic|shopping|local|weather|stocks|technical|general",
  "entities": ["list", "of", "key", "entities"],
  "searchType": "factual|current|tutorial|product|location|technical",
  "timeframe": "recent|historical|current|any",
  "complexity": "simple|moderate|complex",
  "suggestedSites": ["list", "of", "relevant", "domains"],
  "keywords": ["optimized", "search", "keywords"],
  "confidence": 0.95
}

Focus on understanding what the user really wants to know and suggest the best search strategy.
`;

      const response = await this.apiManager.sendRequest(analysisPrompt, {
        role: 'system',
        maxTokens: 500,
        temperature: 0.3
      });

      if (response && typeof response === 'string') {
        try {
          // Try to parse JSON response
          const jsonMatch = response.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const parsed = JSON.parse(jsonMatch[0]);
            // Validate the parsed response has required fields
            if (parsed.intent && parsed.entities && parsed.keywords) {
              return parsed;
            }
          }
        } catch (parseError) {
          console.warn('Failed to parse LLM analysis, using fallback:', parseError);
        }
      }

      // Fallback analysis
      return this.fallbackQueryAnalysis(query);

    } catch (error) {
      console.error('LLM query analysis failed:', error);
      return this.fallbackQueryAnalysis(query);
    }
  }

  /**
   * Fallback query analysis when LLM fails
   * @param {string} query - User's search query
   * @returns {Object} - Basic query analysis
   */
  fallbackQueryAnalysis(query) {
    const lowerQuery = query.toLowerCase();
    
    // Detect intent using patterns
    let intent = 'general';
    for (const [intentType, pattern] of Object.entries(this.intentPatterns)) {
      if (pattern.test(lowerQuery)) {
        intent = intentType;
        break;
      }
    }

    // Extract basic entities (words longer than 3 characters)
    const entities = query.split(/\s+/)
      .filter(word => word.length > 3 && !/^(the|and|for|with|from|that|this|what|how|when|where|why)$/i.test(word))
      .map(word => word.replace(/[^\w]/g, ''));

    return {
      intent: intent,
      entities: entities,
      searchType: intent === 'news' ? 'current' : 'factual',
      timeframe: intent === 'news' ? 'recent' : 'any',
      complexity: entities.length > 3 ? 'complex' : 'simple',
      suggestedSites: this.getSuggestedSites(intent),
      keywords: entities,
      confidence: 0.6
    };
  }

  /**
   * Get suggested sites based on intent
   * @param {string} intent - Query intent
   * @returns {Array} - List of suggested domains
   */
  getSuggestedSites(intent) {
    const siteMap = {
      definition: ['wikipedia.org', 'dictionary.com', 'britannica.com'],
      technical: ['stackoverflow.com', 'github.com', 'docs.microsoft.com'],
      news: ['reuters.com', 'bbc.com', 'cnn.com'],
      academic: ['scholar.google.com', 'jstor.org', 'pubmed.ncbi.nlm.nih.gov'],
      shopping: ['amazon.com', 'ebay.com', 'walmart.com'],
      howTo: ['wikihow.com', 'youtube.com', 'instructables.com']
    };
    
    return siteMap[intent] || ['wikipedia.org', 'reddit.com'];
  }
}

  /**
   * Generate optimized search queries based on analysis
   * @param {Object} analysis - Query analysis results
   * @returns {Promise<Array>} - Array of optimized search queries
   */
  async generateSearchQueries(analysis) {
    const queries = [];
    const baseKeywords = analysis.keywords.join(' ');

    // Primary query - direct search
    queries.push({
      query: baseKeywords,
      type: 'primary',
      operators: []
    });

    // Site-specific queries based on intent
    if (analysis.suggestedSites && analysis.suggestedSites.length > 0) {
      analysis.suggestedSites.slice(0, 2).forEach(site => {
        queries.push({
          query: `${this.searchOperators.site}${site} ${baseKeywords}`,
          type: 'site_specific',
          operators: ['site'],
          site: site
        });
      });
    }

    // Intent-specific queries
    switch (analysis.intent) {
      case 'definition':
        queries.push({
          query: `${this.searchOperators.define}${baseKeywords}`,
          type: 'definition',
          operators: ['define']
        });
        break;

      case 'news':
        queries.push({
          query: `${baseKeywords} ${this.searchOperators.after}2024-01-01`,
          type: 'recent_news',
          operators: ['after']
        });
        break;

      case 'technical':
        queries.push({
          query: `${this.searchOperators.site}stackoverflow.com ${baseKeywords}`,
          type: 'technical_help',
          operators: ['site']
        });
        break;

      case 'academic':
        queries.push({
          query: `${this.searchOperators.filetype}pdf ${baseKeywords}`,
          type: 'academic_papers',
          operators: ['filetype']
        });
        break;
    }

    // Add comparison query if multiple entities
    if (analysis.entities.length > 1) {
      queries.push({
        query: `${analysis.entities.join(' vs ')} comparison`,
        type: 'comparison',
        operators: []
      });
    }

    return queries.slice(0, 5); // Limit to 5 queries max
  }

  /**
   * Perform advanced searches using Google dorking techniques
   * @param {Array} searchQueries - Array of search query objects
   * @param {Function} onProgress - Progress callback
   * @returns {Promise<Array>} - Combined search results
   */
  async performAdvancedSearches(searchQueries, onProgress) {
    const allResults = [];

    for (let i = 0; i < searchQueries.length; i++) {
      const queryObj = searchQueries[i];

      onProgress({
        stage: 'searching',
        message: `Executing ${queryObj.type} search (${i + 1}/${searchQueries.length})...`
      });

      try {
        // Use the existing web search functionality
        const response = await chrome.runtime.sendMessage({
          action: 'webSearch',
          query: queryObj.query,
          numResults: 5
        });

        if (response && response.success && response.results) {
          // Add metadata to results
          const enhancedResults = response.results.map(result => ({
            ...result,
            searchType: queryObj.type,
            searchQuery: queryObj.query,
            operators: queryObj.operators,
            reliability: this.calculateReliability(result.url),
            timestamp: new Date()
          }));

          allResults.push(...enhancedResults);
        }
      } catch (error) {
        console.error(`Search failed for query: ${queryObj.query}`, error);
      }

      // Small delay between searches to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Remove duplicates and sort by reliability
    const uniqueResults = this.removeDuplicateResults(allResults);
    return uniqueResults.sort((a, b) => b.reliability - a.reliability).slice(0, 10);
  }

  /**
   * Calculate reliability score for a URL
   * @param {string} url - URL to score
   * @returns {number} - Reliability score (0-1)
   */
  calculateReliability(url) {
    try {
      const domain = new URL(url).hostname.replace('www.', '');
      return this.sourceReliability[domain] || 0.5;
    } catch (error) {
      return 0.3; // Low score for invalid URLs
    }
  }

  /**
   * Remove duplicate search results
   * @param {Array} results - Array of search results
   * @returns {Array} - Deduplicated results
   */
  removeDuplicateResults(results) {
    const seen = new Set();
    return results.filter(result => {
      const key = result.url;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Fetch and analyze content from search results
   * @param {Array} searchResults - Array of search results
   * @param {Function} onProgress - Progress callback
   * @returns {Promise<Array>} - Analyzed content from sources
   */
  async fetchAndAnalyzeContent(searchResults, onProgress) {
    const analyzedSources = [];
    const topResults = searchResults.slice(0, 5); // Analyze top 5 results

    for (let i = 0; i < topResults.length; i++) {
      const result = topResults[i];

      onProgress({
        stage: 'fetching',
        message: `Analyzing content from ${new URL(result.url).hostname} (${i + 1}/${topResults.length})...`
      });

      try {
        // Fetch webpage content
        const response = await chrome.runtime.sendMessage({
          action: 'fetchWebpage',
          url: result.url,
          includeImages: false,
          deepAnalysis: true
        });

        if (response && response.success && response.content) {
          // Extract key information
          const extractedInfo = await this.extractKeyInformation(
            response.content,
            this.currentSession.query
          );

          analyzedSources.push({
            url: result.url,
            title: result.title,
            snippet: result.snippet,
            content: response.content.substring(0, 2000), // Limit content length
            extractedInfo: extractedInfo,
            reliability: result.reliability,
            searchType: result.searchType,
            timestamp: new Date()
          });
        }
      } catch (error) {
        console.error(`Failed to fetch content from ${result.url}:`, error);

        // Add basic info even if content fetch fails
        analyzedSources.push({
          url: result.url,
          title: result.title,
          snippet: result.snippet,
          content: result.snippet,
          extractedInfo: { summary: result.snippet, keyPoints: [] },
          reliability: result.reliability,
          searchType: result.searchType,
          error: error.message,
          timestamp: new Date()
        });
      }

      // Small delay between fetches
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    return analyzedSources;
  }

  /**
   * Extract key information from webpage content using LLM
   * @param {string} content - Webpage content
   * @param {string} originalQuery - Original user query
   * @returns {Promise<Object>} - Extracted key information
   */
  async extractKeyInformation(content, originalQuery) {
    try {
      const extractionPrompt = `
Extract key information from this webpage content that's relevant to the query: "${originalQuery}"

Content (first 1500 chars):
${content.substring(0, 1500)}

Please provide a JSON response with:
{
  "summary": "Brief summary of relevant information",
  "keyPoints": ["list", "of", "key", "points"],
  "relevanceScore": 0.85,
  "facts": ["specific", "facts", "or", "data"],
  "quotes": ["important", "quotes", "if", "any"]
}

Focus only on information that directly answers or relates to the original query.
`;

      const response = await this.apiManager.sendRequest(extractionPrompt, { role: 'system' });

      try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (parseError) {
        console.warn('Failed to parse content extraction, using fallback');
      }

      // Fallback extraction
      return this.fallbackContentExtraction(content, originalQuery);

    } catch (error) {
      console.error('Content extraction failed:', error);
      return this.fallbackContentExtraction(content, originalQuery);
    }
  }

  /**
   * Fallback content extraction when LLM fails
   * @param {string} content - Webpage content
   * @param {string} originalQuery - Original user query
   * @returns {Object} - Basic extracted information
   */
  fallbackContentExtraction(content, originalQuery) {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
    const queryWords = originalQuery.toLowerCase().split(/\s+/);

    // Find sentences that contain query words
    const relevantSentences = sentences.filter(sentence => {
      const lowerSentence = sentence.toLowerCase();
      return queryWords.some(word => lowerSentence.includes(word));
    }).slice(0, 3);

    return {
      summary: relevantSentences[0] || content.substring(0, 200),
      keyPoints: relevantSentences,
      relevanceScore: relevantSentences.length > 0 ? 0.7 : 0.3,
      facts: [],
      quotes: []
    };
  }

  /**
   * Generate comprehensive answer using analyzed content
   * @param {Object} queryAnalysis - Original query analysis
   * @param {Array} analyzedContent - Analyzed content from sources
   * @param {string} originalQuery - Original user query
   * @returns {Promise<Object>} - Comprehensive answer
   */
  async generateComprehensiveAnswer(queryAnalysis, analyzedContent, originalQuery) {
    try {
      // Prepare context from all sources
      const sourceContext = analyzedContent.map((source, index) => {
        return `
Source ${index + 1}: ${source.title} (${new URL(source.url).hostname})
Reliability: ${(source.reliability * 100).toFixed(0)}%
Summary: ${source.extractedInfo.summary}
Key Points: ${source.extractedInfo.keyPoints.join('; ')}
`;
      }).join('\n');

      const answerPrompt = `
Based on the following sources, provide a comprehensive answer to the user's question: "${originalQuery}"

Query Analysis:
- Intent: ${queryAnalysis.intent}
- Complexity: ${queryAnalysis.complexity}
- Search Type: ${queryAnalysis.searchType}

Sources:
${sourceContext}

Please provide a JSON response with:
{
  "answer": "Comprehensive answer to the user's question",
  "keyFindings": ["list", "of", "key", "findings"],
  "confidence": 0.9,
  "sources": [
    {
      "title": "Source title",
      "url": "source url",
      "relevance": "why this source is relevant"
    }
  ],
  "followUpQuestions": ["suggested", "follow", "up", "questions"],
  "limitations": "Any limitations or caveats about the answer"
}

Make the answer informative, well-structured, and cite sources appropriately.
`;

      const response = await this.apiManager.sendRequest(answerPrompt, { role: 'system' });

      try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const parsed = JSON.parse(jsonMatch[0]);

          // Enhance with source metadata
          parsed.sources = analyzedContent.map(source => ({
            title: source.title,
            url: source.url,
            hostname: new URL(source.url).hostname,
            reliability: source.reliability,
            searchType: source.searchType,
            relevance: source.extractedInfo.relevanceScore
          }));

          return parsed;
        }
      } catch (parseError) {
        console.warn('Failed to parse answer generation, using fallback');
      }

      // Fallback answer generation
      return this.fallbackAnswerGeneration(analyzedContent, originalQuery);

    } catch (error) {
      console.error('Answer generation failed:', error);
      return this.fallbackAnswerGeneration(analyzedContent, originalQuery);
    }
  }

  /**
   * Fallback answer generation when LLM fails
   * @param {Array} analyzedContent - Analyzed content from sources
   * @param {string} originalQuery - Original user query
   * @returns {Object} - Basic answer
   */
  fallbackAnswerGeneration(analyzedContent, originalQuery) {
    const summaries = analyzedContent
      .filter(source => source.extractedInfo.relevanceScore > 0.5)
      .map(source => source.extractedInfo.summary)
      .slice(0, 3);

    const answer = summaries.length > 0
      ? `Based on the search results: ${summaries.join(' ')}`
      : 'I found some relevant information, but couldn\'t generate a comprehensive answer. Please check the sources below.';

    return {
      answer: answer,
      keyFindings: summaries,
      confidence: summaries.length > 0 ? 0.6 : 0.3,
      sources: analyzedContent.map(source => ({
        title: source.title,
        url: source.url,
        hostname: new URL(source.url).hostname,
        reliability: source.reliability,
        searchType: source.searchType,
        relevance: source.extractedInfo.relevanceScore
      })),
      followUpQuestions: ['Can you provide more specific information?', 'What are the latest updates on this topic?'],
      limitations: 'This answer was generated using basic text analysis due to AI processing limitations.'
    };
  }

  /**
   * Get search history
   * @returns {Array} - Array of previous search sessions
   */
  getSearchHistory() {
    return this.searchHistory;
  }

  /**
   * Clear search history
   */
  clearSearchHistory() {
    this.searchHistory = [];
  }

  /**
   * Get current session details
   * @returns {Object|null} - Current session or null
   */
  getCurrentSession() {
    return this.currentSession;
  }

  /**
   * Test mode for immediate feedback without API dependencies
   * @param {string} query - User's search query
   * @param {Function} onProgress - Progress callback
   * @returns {Promise<Object>} - Test mode results
   */
  async runTestMode(query, onProgress) {
    console.log('🧪 Running AI Agent Browser in test mode');

    // Simulate progress
    onProgress({ stage: 'analyzing', message: 'Analyzing query (test mode)...' });
    await new Promise(resolve => setTimeout(resolve, 500));

    onProgress({ stage: 'planning', message: 'Planning search strategy (test mode)...' });
    await new Promise(resolve => setTimeout(resolve, 500));

    onProgress({ stage: 'searching', message: 'Searching web sources (test mode)...' });
    await new Promise(resolve => setTimeout(resolve, 1000));

    onProgress({ stage: 'fetching', message: 'Analyzing content (test mode)...' });
    await new Promise(resolve => setTimeout(resolve, 800));

    onProgress({ stage: 'synthesizing', message: 'Generating response (test mode)...' });
    await new Promise(resolve => setTimeout(resolve, 600));

    onProgress({ stage: 'complete', message: 'Test mode complete!' });

    // Generate test response
    const testAnalysis = this.fallbackQueryAnalysis(query);
    const testSources = this.generateTestSources(query);
    const testAnswer = this.generateTestAnswer(query, testAnalysis);

    return {
      success: true,
      sessionId: this.currentSession.id,
      query: query,
      analysis: testAnalysis,
      sources: testSources,
      answer: testAnswer,
      searchQueries: [`${query} test mode`],
      timestamp: new Date(),
      testMode: true
    };
  }

  /**
   * Generate test sources for demo purposes
   * @param {string} query - User's search query
   * @returns {Array} - Test sources
   */
  generateTestSources(query) {
    return [
      {
        title: `${query} - Wikipedia`,
        url: `https://en.wikipedia.org/wiki/${encodeURIComponent(query)}`,
        hostname: 'wikipedia.org',
        reliability: 0.9,
        searchType: 'primary',
        snippet: `Wikipedia article about ${query}. This is a test source generated for demonstration purposes.`,
        extractedInfo: {
          summary: `This is a comprehensive overview of ${query} from Wikipedia.`,
          keyPoints: [`Key information about ${query}`, 'Important details and context', 'Related concepts and applications'],
          relevanceScore: 0.9
        }
      },
      {
        title: `Understanding ${query} - Stack Overflow`,
        url: `https://stackoverflow.com/questions/tagged/${encodeURIComponent(query)}`,
        hostname: 'stackoverflow.com',
        reliability: 0.9,
        searchType: 'technical',
        snippet: `Technical discussion and solutions related to ${query}.`,
        extractedInfo: {
          summary: `Technical insights and practical solutions for ${query}.`,
          keyPoints: ['Technical implementation details', 'Best practices and recommendations', 'Common issues and solutions'],
          relevanceScore: 0.8
        }
      },
      {
        title: `${query} Guide - Medium`,
        url: `https://medium.com/search?q=${encodeURIComponent(query)}`,
        hostname: 'medium.com',
        reliability: 0.7,
        searchType: 'tutorial',
        snippet: `In-depth guide and tutorial about ${query}.`,
        extractedInfo: {
          summary: `Practical guide and tutorial covering ${query}.`,
          keyPoints: ['Step-by-step instructions', 'Real-world examples', 'Tips and tricks'],
          relevanceScore: 0.7
        }
      }
    ];
  }

  /**
   * Generate test answer for demo purposes
   * @param {string} query - User's search query
   * @param {Object} analysis - Query analysis
   * @returns {Object} - Test answer
   */
  generateTestAnswer(query, analysis) {
    return {
      answer: `Based on my analysis of "${query}", here's what I found:\n\nThis is a ${analysis.complexity} query about ${analysis.intent}. The search revealed comprehensive information from multiple reliable sources including Wikipedia, Stack Overflow, and Medium.\n\n**Note: This is a test mode response.** The AI Agent Browser is currently running in demonstration mode. To get real search results and AI-powered analysis, please ensure your API keys are properly configured.`,
      keyFindings: [
        `Query type identified as: ${analysis.intent}`,
        `Complexity level: ${analysis.complexity}`,
        `Search strategy optimized for: ${analysis.searchType}`,
        'Multiple high-quality sources analyzed',
        'Test mode provides immediate feedback for development'
      ],
      confidence: 0.7,
      followUpQuestions: [
        `How does ${query} work in practice?`,
        `What are the benefits of ${query}?`,
        `Are there alternatives to ${query}?`,
        `What are common issues with ${query}?`
      ],
      limitations: 'This response was generated in test mode for demonstration purposes. Real AI analysis requires proper API configuration.'
    };
  }
}

// Export the class
window.AIAgentBrowser = AIAgentBrowser;

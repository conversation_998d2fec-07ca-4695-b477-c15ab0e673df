# AI Agent Browser - Perplexity Comet-like Browsing Agent

## Overview

The AI Agent Browser is an intelligent web browsing system inspired by <PERSON><PERSON><PERSON>'s Comet browser. It provides AI-powered search capabilities with advanced Google dorking techniques, content analysis, and comprehensive answer generation with source citations.

## Features

### 🧠 Intelligent Query Understanding
- **LLM-Powered Analysis**: Uses your configured LLM to understand user intent and extract key entities
- **Intent Detection**: Automatically detects query types (definition, comparison, how-to, news, academic, etc.)
- **Search Strategy Optimization**: Determines the best search approach based on query analysis

### 🔍 Advanced Search Capabilities
- **Google Dorking**: Implements advanced search operators (site:, filetype:, intitle:, etc.)
- **Multi-Query Strategy**: Generates multiple optimized search queries for comprehensive results
- **Source Reliability Scoring**: Evaluates and ranks sources based on domain authority
- **Fallback Search APIs**: Supports SerpAPI, Google Custom Search, and DuckDuckGo

### 📊 Content Analysis & Synthesis
- **Web Content Fetching**: Retrieves and analyzes content from top search results
- **LLM-Powered Extraction**: Extracts key information relevant to the original query
- **Source Deduplication**: Removes duplicate results and consolidates information
- **Relevance Scoring**: Scores content relevance to the user's query

### 🎯 Comprehensive Response Generation
- **Multi-Source Synthesis**: Combines information from multiple sources
- **Source Citations**: Provides clickable links to original sources
- **Confidence Scoring**: Shows confidence levels for generated answers
- **Follow-up Suggestions**: Suggests related questions for deeper exploration

## Architecture

### Core Components

1. **AIAgentBrowser** (`scripts/ai-agent-browser.js`)
   - Main orchestrator for the browsing process
   - Manages search sessions and progress tracking
   - Coordinates between different components

2. **GoogleAdvancedSearch** (`scripts/google-advanced-search.js`)
   - Implements Google dorking techniques
   - Generates optimized search queries
   - Provides search operator utilities

3. **Enhanced Background Search** (`background/background.js`)
   - Multiple search API support (SerpAPI, Google CSE, DuckDuckGo)
   - Fallback mechanisms for reliability
   - Content fetching and processing

### Search Process Flow

```
User Query → LLM Analysis → Query Generation → Multi-API Search → Content Fetching → LLM Analysis → Response Generation
```

1. **Query Analysis**: LLM analyzes user intent and extracts entities
2. **Search Planning**: Generates multiple optimized search queries
3. **Multi-Source Search**: Executes searches across different APIs
4. **Content Retrieval**: Fetches content from top results
5. **Information Extraction**: LLM extracts relevant information
6. **Response Synthesis**: Generates comprehensive answer with citations

## Usage

### Basic Usage

1. Click the robot icon (🤖) in the extension toolbar
2. Type your question in the AI Agent Browser interface
3. Watch the real-time progress as the agent:
   - Analyzes your query
   - Plans search strategy
   - Searches multiple sources
   - Analyzes content
   - Generates comprehensive response

### Example Queries

- **News**: "What are the latest developments in artificial intelligence?"
- **How-to**: "How to learn Python programming for beginners"
- **Comparison**: "Compare React vs Vue.js for web development"
- **Definition**: "What is quantum computing?"
- **Technical**: "Best practices for machine learning model deployment"

### Advanced Features

#### Google Dorking Examples
```javascript
// Academic research
site:arxiv.org machine learning filetype:pdf

// Recent news
artificial intelligence after:2024-01-01

// Technical solutions
site:stackoverflow.com intitle:"python error"

// Government data
site:gov climate change filetype:pdf
```

#### Search Operators
- `site:domain.com` - Search specific website
- `filetype:pdf` - Find specific file types
- `intitle:"exact phrase"` - Search in page titles
- `inurl:keyword` - Search in URLs
- `after:2024-01-01` - Date filtering
- `"exact phrase"` - Exact phrase matching

## Configuration

### API Keys (Optional)
For enhanced search capabilities, configure these API keys in your environment:

```javascript
// SerpAPI (Recommended)
SERPAPI_KEY=your_serpapi_key_here

// Google Custom Search
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_CSE_ID=your_custom_search_engine_id_here
```

### Fallback Behavior
The system works without API keys by using:
1. DuckDuckGo Instant Answer API (free, limited)
2. Simulated search results for demonstration

## Technical Details

### Search API Priority
1. **SerpAPI** - Most comprehensive, requires API key
2. **Google Custom Search** - Good quality, requires setup
3. **DuckDuckGo** - Free but limited results
4. **Simulated Results** - Fallback for demonstration

### Source Reliability Scoring
```javascript
const sourceReliability = {
  'wikipedia.org': 0.9,
  'stackoverflow.com': 0.9,
  'github.com': 0.8,
  'reddit.com': 0.7,
  'medium.com': 0.7,
  // ... more domains
};
```

### Content Processing
- Fetches up to 5 top results per search query
- Extracts up to 2000 characters per source
- Uses LLM for intelligent content summarization
- Removes duplicate and irrelevant content

## UI Components

### Progress Indicator
Shows real-time progress through search stages:
- 🔍 Analyzing query
- 📋 Planning search strategy
- 🌐 Searching multiple sources
- 📊 Analyzing content
- ✨ Generating response

### Response Display
- **Answer Section**: Comprehensive answer to the query
- **Key Findings**: Bullet points of important information
- **Sources**: Clickable links with reliability scores
- **Follow-up Questions**: Suggested related queries
- **Limitations**: Any caveats about the answer

### Interactive Elements
- **Source Citations**: Click to visit original sources
- **Follow-up Questions**: Click to ask related questions
- **View Details**: See detailed search session information

## Development

### Testing
Use the demo functionality to test the system:

```javascript
// In browser console
const demo = new AIAgentDemo();
demo.showCapabilities();
demo.testAdvancedSearch();
demo.runDemo(apiManager);
```

### Extending Functionality

#### Adding New Search Operators
```javascript
// In GoogleAdvancedSearch class
this.operators.newOperator = (value) => `newoperator:${value}`;
```

#### Custom Source Reliability
```javascript
// Add new domains to reliability scoring
this.sourceReliability['newdomain.com'] = 0.8;
```

#### Intent Patterns
```javascript
// Add new intent detection patterns
this.intentPatterns.newIntent = /^(pattern|keywords)/i;
```

## Troubleshooting

### Common Issues

1. **No Search Results**
   - Check internet connection
   - Verify API keys if using paid services
   - Try simpler queries

2. **Slow Performance**
   - Reduce number of search queries
   - Limit content fetching depth
   - Check LLM API response times

3. **Poor Answer Quality**
   - Ensure LLM is properly configured
   - Try more specific queries
   - Check source reliability scores

### Debug Mode
Enable debug logging in browser console:
```javascript
// Enable detailed logging
localStorage.setItem('aiAgentDebug', 'true');
```

## Future Enhancements

- [ ] Real-time streaming responses
- [ ] Image and video search integration
- [ ] Multi-language support
- [ ] Custom search engine configuration
- [ ] Advanced filtering options
- [ ] Search result caching
- [ ] Collaborative search sessions

## Contributing

To contribute to the AI Agent Browser:

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests and documentation
5. Submit a pull request

## License

This project is part of the BrowzyAI extension and follows the same licensing terms.

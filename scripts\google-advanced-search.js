'use strict';

/**
 * Google Advanced Search Service
 * Implements Google dorking techniques and advanced search operators
 * for more precise and targeted search results
 */
class GoogleAdvancedSearch {
  constructor() {
    // Advanced search operators
    this.operators = {
      // Site-specific search
      site: (domain) => `site:${domain}`,
      
      // File type search
      filetype: (type) => `filetype:${type}`,
      
      // Title search
      intitle: (text) => `intitle:"${text}"`,
      allintitle: (text) => `allintitle:${text}`,
      
      // URL search
      inurl: (text) => `inurl:${text}`,
      allinurl: (text) => `allinurl:${text}`,
      
      // Text search
      intext: (text) => `intext:"${text}"`,
      allintext: (text) => `allintext:${text}`,
      
      // Anchor text
      inanchor: (text) => `inanchor:"${text}"`,
      allinanchor: (text) => `allinanchor:${text}`,
      
      // Related sites
      related: (domain) => `related:${domain}`,
      
      // Cache
      cache: (url) => `cache:${url}`,
      
      // Info about site
      info: (domain) => `info:${domain}`,
      
      // Links to site
      link: (domain) => `link:${domain}`,
      
      // Date ranges
      after: (date) => `after:${date}`,
      before: (date) => `before:${date}`,
      
      // Exact phrase
      exact: (phrase) => `"${phrase}"`,
      
      // Exclude terms
      exclude: (term) => `-${term}`,
      
      // OR operator
      or: (term1, term2) => `${term1} OR ${term2}`,
      
      // Wildcard
      wildcard: (pattern) => pattern.replace(/\*/g, '*'),
      
      // Number ranges
      numrange: (min, max) => `${min}..${max}`,
      
      // Location
      location: (place) => `location:"${place}"`,
      
      // Weather
      weather: (location) => `weather:${location}`,
      
      // Stocks
      stocks: (symbol) => `stocks:${symbol}`,
      
      // Maps
      map: (location) => `map:${location}`,
      
      // Movies
      movie: (title) => `movie:${title}`,
      
      // Define
      define: (term) => `define:${term}`,
      
      // Calculator
      calc: (expression) => expression,
      
      // Unit conversion
      convert: (expression) => expression
    };

    // Common file types for different search intents
    this.fileTypes = {
      academic: ['pdf', 'doc', 'docx', 'ppt', 'pptx'],
      data: ['csv', 'xls', 'xlsx', 'json', 'xml'],
      code: ['js', 'py', 'java', 'cpp', 'html', 'css'],
      media: ['mp4', 'mp3', 'jpg', 'png', 'gif'],
      documents: ['pdf', 'doc', 'docx', 'txt', 'rtf']
    };

    // High-quality domains for different topics
    this.qualityDomains = {
      academic: [
        'scholar.google.com',
        'jstor.org',
        'pubmed.ncbi.nlm.nih.gov',
        'arxiv.org',
        'researchgate.net',
        'academia.edu',
        'ieee.org',
        'acm.org'
      ],
      news: [
        'reuters.com',
        'bbc.com',
        'cnn.com',
        'npr.org',
        'theguardian.com',
        'nytimes.com',
        'washingtonpost.com',
        'apnews.com'
      ],
      technical: [
        'stackoverflow.com',
        'github.com',
        'docs.microsoft.com',
        'developer.mozilla.org',
        'w3schools.com',
        'medium.com',
        'dev.to',
        'hackernoon.com'
      ],
      reference: [
        'wikipedia.org',
        'britannica.com',
        'dictionary.com',
        'merriam-webster.com',
        'investopedia.com',
        'howstuffworks.com'
      ],
      government: [
        'gov',
        'edu',
        'who.int',
        'cdc.gov',
        'fda.gov',
        'nasa.gov'
      ]
    };

    // Search patterns for different query types
    this.searchPatterns = {
      definition: {
        operators: ['define', 'site'],
        sites: this.qualityDomains.reference,
        modifiers: ['meaning', 'definition', 'what is']
      },
      
      howTo: {
        operators: ['site', 'intitle'],
        sites: ['wikihow.com', 'youtube.com', 'instructables.com'],
        modifiers: ['how to', 'tutorial', 'guide', 'step by step']
      },
      
      news: {
        operators: ['site', 'after'],
        sites: this.qualityDomains.news,
        modifiers: ['latest', 'recent', 'news', 'update']
      },
      
      academic: {
        operators: ['site', 'filetype'],
        sites: this.qualityDomains.academic,
        fileTypes: this.fileTypes.academic,
        modifiers: ['research', 'study', 'paper', 'analysis']
      },
      
      technical: {
        operators: ['site', 'inurl'],
        sites: this.qualityDomains.technical,
        modifiers: ['error', 'fix', 'solution', 'troubleshoot']
      },
      
      shopping: {
        operators: ['site', 'intitle'],
        sites: ['amazon.com', 'ebay.com', 'walmart.com', 'target.com'],
        modifiers: ['buy', 'price', 'review', 'best']
      },
      
      local: {
        operators: ['location', 'map'],
        modifiers: ['near me', 'nearby', 'local', 'address']
      },
      
      comparison: {
        operators: ['site', 'intitle'],
        sites: this.qualityDomains.reference.concat(['reddit.com', 'quora.com']),
        modifiers: ['vs', 'versus', 'compare', 'comparison', 'difference']
      }
    };
  }

  /**
   * Generate advanced search queries based on intent and analysis
   * @param {Object} queryAnalysis - Analysis of the user's query
   * @returns {Array} - Array of optimized search query objects
   */
  generateAdvancedQueries(queryAnalysis) {
    const queries = [];
    const { intent, entities, keywords, timeframe, searchType } = queryAnalysis;
    
    // Get pattern for this intent
    const pattern = this.searchPatterns[intent] || this.searchPatterns.general;
    
    // Base query with keywords
    const baseKeywords = keywords.join(' ');
    
    // 1. Primary query - enhanced with intent-specific modifiers
    if (pattern.modifiers) {
      const modifier = pattern.modifiers[0];
      queries.push({
        query: `${modifier} ${baseKeywords}`,
        type: 'primary_enhanced',
        intent: intent,
        operators: []
      });
    }

    // 2. Site-specific queries for high-quality sources
    if (pattern.sites) {
      pattern.sites.slice(0, 3).forEach(site => {
        queries.push({
          query: this.operators.site(site) + ' ' + baseKeywords,
          type: 'site_specific',
          intent: intent,
          operators: ['site'],
          targetSite: site
        });
      });
    }

    // 3. File type specific queries for academic content
    if (pattern.fileTypes && intent === 'academic') {
      pattern.fileTypes.slice(0, 2).forEach(fileType => {
        queries.push({
          query: this.operators.filetype(fileType) + ' ' + baseKeywords,
          type: 'filetype_specific',
          intent: intent,
          operators: ['filetype'],
          fileType: fileType
        });
      });
    }

    // 4. Time-sensitive queries
    if (timeframe === 'recent' || intent === 'news') {
      const currentYear = new Date().getFullYear();
      queries.push({
        query: this.operators.after(`${currentYear}-01-01`) + ' ' + baseKeywords,
        type: 'time_filtered',
        intent: intent,
        operators: ['after'],
        timeframe: 'recent'
      });
    }

    // 5. Exact phrase queries for specific searches
    if (entities.length > 1) {
      const exactPhrase = entities.join(' ');
      queries.push({
        query: this.operators.exact(exactPhrase),
        type: 'exact_phrase',
        intent: intent,
        operators: ['exact']
      });
    }

    // 6. Title-specific queries for more relevant results
    queries.push({
      query: this.operators.intitle(baseKeywords),
      type: 'title_specific',
      intent: intent,
      operators: ['intitle']
    });

    // 7. Exclude common noise terms
    const noiseTerms = ['wiki', 'wikipedia'] // Add more as needed
    if (intent !== 'definition') {
      noiseTerms.forEach(term => {
        queries.push({
          query: `${baseKeywords} ${this.operators.exclude(term)}`,
          type: 'noise_filtered',
          intent: intent,
          operators: ['exclude']
        });
      });
    }

    return queries.slice(0, 8); // Limit to 8 queries to avoid overwhelming
  }

  /**
   * Build a dorking query for specific information gathering
   * @param {Object} params - Parameters for dorking
   * @returns {string} - Constructed dorking query
   */
  buildDorkingQuery(params) {
    const {
      keywords,
      site,
      fileType,
      inTitle,
      inUrl,
      inText,
      excludeTerms = [],
      dateAfter,
      dateBefore,
      exactPhrase
    } = params;

    let query = '';

    // Add exact phrase if specified
    if (exactPhrase) {
      query += this.operators.exact(exactPhrase) + ' ';
    }

    // Add keywords
    if (keywords && !exactPhrase) {
      query += keywords.join(' ') + ' ';
    }

    // Add site restriction
    if (site) {
      query += this.operators.site(site) + ' ';
    }

    // Add file type
    if (fileType) {
      query += this.operators.filetype(fileType) + ' ';
    }

    // Add title search
    if (inTitle) {
      query += this.operators.intitle(inTitle) + ' ';
    }

    // Add URL search
    if (inUrl) {
      query += this.operators.inurl(inUrl) + ' ';
    }

    // Add text search
    if (inText) {
      query += this.operators.intext(inText) + ' ';
    }

    // Add date filters
    if (dateAfter) {
      query += this.operators.after(dateAfter) + ' ';
    }

    if (dateBefore) {
      query += this.operators.before(dateBefore) + ' ';
    }

    // Exclude terms
    excludeTerms.forEach(term => {
      query += this.operators.exclude(term) + ' ';
    });

    return query.trim();
  }

  /**
   * Get specialized queries for vulnerability research (ethical hacking)
   * @param {string} target - Target domain or technology
   * @returns {Array} - Array of security research queries
   */
  getSecurityResearchQueries(target) {
    // Note: These are for educational and ethical security research only
    return [
      this.buildDorkingQuery({
        site: target,
        inUrl: 'admin',
        keywords: ['login', 'panel']
      }),
      this.buildDorkingQuery({
        site: target,
        fileType: 'log',
        keywords: ['error', 'debug']
      }),
      this.buildDorkingQuery({
        site: target,
        inTitle: 'index of',
        keywords: ['config', 'backup']
      })
    ];
  }

  /**
   * Get academic research queries
   * @param {string} topic - Research topic
   * @returns {Array} - Array of academic search queries
   */
  getAcademicQueries(topic) {
    const queries = [];
    
    // Search in academic databases
    this.qualityDomains.academic.forEach(site => {
      queries.push(this.buildDorkingQuery({
        site: site,
        keywords: [topic],
        fileType: 'pdf'
      }));
    });

    // Search for specific academic file types
    this.fileTypes.academic.forEach(fileType => {
      queries.push(this.buildDorkingQuery({
        keywords: [topic, 'research'],
        fileType: fileType,
        inTitle: topic
      }));
    });

    return queries;
  }
}

// Export the class
window.GoogleAdvancedSearch = GoogleAdvancedSearch;

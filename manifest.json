{"manifest_version": 3, "name": "BrowzyAI", "version": "1.2", "description": "BrowzyAI - Your intelligent AI assistant for any task, powered by multiple AI models", "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; frame-ancestors 'self'; img-src 'self' data: blob:; connect-src 'self' https://*.openai.com https://openrouter.ai https://*.openrouter.ai https://*.googleapis.com https://*.google.com https://*.anthropic.com https://*.mistral.ai https://*.deepseek.com https://*.cohere.com https://*.huggingface.co https://huggingface.co https://*.browzyai.com https://*.koyeb.app"}, "icons": {"16": "images/icon16.png", "32": "images/icon.png", "48": "images/icon48.png", "128": "images/icon128.png"}, "permissions": ["storage", "activeTab", "scripting", "tabs", "clipboardWrite", "history", "contextMenus", "sidePanel", "notifications"], "optional_permissions": [], "host_permissions": ["https://*.openai.com/*", "https://*.anthropic.com/*", "https://*.koyeb.app/*", "https://*.googleapis.com/*", "https://*.google.com/*", "https://*.mistral.ai/*", "https://*.deepseek.com/*", "https://*.cohere.com/*", "https://*.huggingface.co/*", "https://openrouter.ai/*", "https://*.browzyai.com/*", "http://localhost:5173/*", "*://*.youtube.com/*", "<all_urls>"], "action": {"default_icon": {"16": "images/icon16.png", "32": "images/icon.png", "48": "images/icon48.png", "128": "images/icon128.png"}}, "side_panel": {"default_path": "popup/popup.html?sidebar=true"}, "background": {"service_worker": "background/background.js", "type": "module", "additional_scripts": ["background/api-handler.js"]}, "content_scripts": [{"matches": ["<all_urls>"], "exclude_matches": ["*://*.youtube.com/*", "*://youtube.com/*"], "js": ["content/content.js", "content/analyzer-content.js"]}, {"matches": ["*://*.youtube.com/*", "*://youtube.com/*"], "js": ["content/youtube-content.js"]}, {"matches": ["<all_urls>"], "include_globs": ["*.pdf", "*pdf.js*", "*viewer.html*"], "js": ["content/pdf-content.js"]}, {"matches": ["*://*.leetcode.com/*", "*://*.hackerrank.com/*", "*://*.codewars.com/*", "*://*.codeforces.com/*", "*://*.hackerearth.com/*", "*://*.topcoder.com/*", "*://*.codingame.com/*", "*://*.geeksforgeeks.org/*", "*://*.codesignal.com/*", "*://*.codechef.com/*", "*://*.replit.com/*", "*://*.codesandbox.io/*", "*://*.jsfiddle.net/*", "*://*.codepen.io/*"], "js": ["content/coding-platform-content.js"]}, {"matches": ["<all_urls>"], "exclude_matches": ["*://*.youtube.com/*", "*://youtube.com/*"], "js": ["content/brave-sidebar.js"], "run_at": "document_end"}, {"matches": ["<all_urls>"], "js": ["content/drawing-tools.js"], "run_at": "document_end"}, {"matches": ["<all_urls>"], "js": ["scripts/web-mcp-content.js"], "run_at": "document_end"}, {"matches": ["<all_urls>"], "js": ["content/video-content.js"], "run_at": "document_end"}], "web_accessible_resources": [{"resources": ["images/*", "content/*", "scripts/*", "content/scanner.js", "popup/*"], "matches": ["<all_urls>", "*://*.youtube.com/*", "*://youtube.com/*"]}]}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Agent Browser Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #e0e0e0;
        }
        .test-container {
            background: #2a2a2a;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-button {
            background: linear-gradient(135deg, #00a6c0, #48d7ce);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        .test-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 166, 192, 0.3);
        }
        .test-output {
            background: #1a1a1a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🤖 AI Agent Browser Test</h1>
    
    <div class="test-container">
        <h2>Component Loading Test</h2>
        <button class="test-button" onclick="testComponentLoading()">Test Component Loading</button>
        <div id="loading-output" class="test-output"></div>
    </div>

    <div class="test-container">
        <h2>Query Analysis Test</h2>
        <button class="test-button" onclick="testQueryAnalysis()">Test Query Analysis</button>
        <div id="analysis-output" class="test-output"></div>
    </div>

    <div class="test-container">
        <h2>Search Operators Test</h2>
        <button class="test-button" onclick="testSearchOperators()">Test Search Operators</button>
        <div id="operators-output" class="test-output"></div>
    </div>

    <div class="test-container">
        <h2>Full AI Agent Test</h2>
        <input type="text" id="test-query" placeholder="Enter your test query..." 
               value="What is artificial intelligence?" 
               style="width: 60%; padding: 10px; margin: 10px; border-radius: 6px; border: 1px solid #444; background: #1a1a1a; color: #e0e0e0;">
        <button class="test-button" onclick="testFullAIAgent()">Run AI Agent Test</button>
        <div id="agent-output" class="test-output"></div>
    </div>

    <!-- Load the AI Agent scripts -->
    <script src="scripts/google-advanced-search.js"></script>
    <script src="scripts/ai-agent-browser.js"></script>
    <script src="scripts/ai-agent-demo.js"></script>

    <script>
        // Mock API Manager for testing
        const mockApiManager = {
            validateApiKey: false,
            sendRequest: async function(prompt, options) {
                // Simulate API response delay
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Return mock response
                if (prompt.includes('analyze') || prompt.includes('Analyze')) {
                    return JSON.stringify({
                        intent: "general",
                        entities: ["artificial", "intelligence"],
                        searchType: "factual",
                        timeframe: "any",
                        complexity: "moderate",
                        suggestedSites: ["wikipedia.org", "britannica.com"],
                        keywords: ["artificial", "intelligence"],
                        confidence: 0.8
                    });
                }
                
                return "This is a mock API response for testing purposes.";
            }
        };

        function log(elementId, message, type = 'info') {
            const output = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            
            output.innerHTML += `<div class="status ${statusClass}">[${timestamp}] ${message}</div>`;
            output.scrollTop = output.scrollHeight;
        }

        function testComponentLoading() {
            const output = document.getElementById('loading-output');
            output.innerHTML = '';
            
            log('loading-output', 'Testing component loading...', 'info');
            
            // Test GoogleAdvancedSearch
            if (window.GoogleAdvancedSearch) {
                log('loading-output', '✅ GoogleAdvancedSearch loaded successfully', 'success');
                try {
                    const advancedSearch = new GoogleAdvancedSearch();
                    log('loading-output', '✅ GoogleAdvancedSearch instance created', 'success');
                } catch (error) {
                    log('loading-output', `❌ GoogleAdvancedSearch instantiation failed: ${error.message}`, 'error');
                }
            } else {
                log('loading-output', '❌ GoogleAdvancedSearch not loaded', 'error');
            }

            // Test AIAgentBrowser
            if (window.AIAgentBrowser) {
                log('loading-output', '✅ AIAgentBrowser loaded successfully', 'success');
                try {
                    const aiAgent = new AIAgentBrowser(mockApiManager);
                    log('loading-output', '✅ AIAgentBrowser instance created', 'success');
                } catch (error) {
                    log('loading-output', `❌ AIAgentBrowser instantiation failed: ${error.message}`, 'error');
                }
            } else {
                log('loading-output', '❌ AIAgentBrowser not loaded', 'error');
            }

            // Test AIAgentDemo
            if (window.AIAgentDemo) {
                log('loading-output', '✅ AIAgentDemo loaded successfully', 'success');
                try {
                    const demo = new AIAgentDemo();
                    log('loading-output', '✅ AIAgentDemo instance created', 'success');
                } catch (error) {
                    log('loading-output', `❌ AIAgentDemo instantiation failed: ${error.message}`, 'error');
                }
            } else {
                log('loading-output', '❌ AIAgentDemo not loaded', 'error');
            }
        }

        function testQueryAnalysis() {
            const output = document.getElementById('analysis-output');
            output.innerHTML = '';
            
            log('analysis-output', 'Testing query analysis...', 'info');
            
            if (!window.AIAgentBrowser) {
                log('analysis-output', '❌ AIAgentBrowser not available', 'error');
                return;
            }

            try {
                const aiAgent = new AIAgentBrowser(mockApiManager);
                const testQuery = "How to learn Python programming?";
                
                log('analysis-output', `Testing query: "${testQuery}"`, 'info');
                
                // Test fallback analysis
                const analysis = aiAgent.fallbackQueryAnalysis(testQuery);
                log('analysis-output', `✅ Fallback analysis completed:`, 'success');
                log('analysis-output', JSON.stringify(analysis, null, 2), 'info');
                
            } catch (error) {
                log('analysis-output', `❌ Query analysis failed: ${error.message}`, 'error');
            }
        }

        function testSearchOperators() {
            const output = document.getElementById('operators-output');
            output.innerHTML = '';
            
            log('operators-output', 'Testing search operators...', 'info');
            
            if (!window.GoogleAdvancedSearch) {
                log('operators-output', '❌ GoogleAdvancedSearch not available', 'error');
                return;
            }

            try {
                const advancedSearch = new GoogleAdvancedSearch();
                
                log('operators-output', 'Testing search operators:', 'info');
                log('operators-output', `site: ${advancedSearch.operators.site('example.com')}`, 'info');
                log('operators-output', `filetype: ${advancedSearch.operators.filetype('pdf')}`, 'info');
                log('operators-output', `intitle: ${advancedSearch.operators.intitle('test')}`, 'info');
                
                // Test query generation
                const testAnalysis = {
                    intent: 'academic',
                    entities: ['machine learning'],
                    keywords: ['machine', 'learning'],
                    timeframe: 'recent',
                    searchType: 'factual'
                };
                
                const queries = advancedSearch.generateAdvancedQueries(testAnalysis);
                log('operators-output', `✅ Generated ${queries.length} search queries:`, 'success');
                queries.forEach((query, index) => {
                    log('operators-output', `${index + 1}. ${query.query} (${query.type})`, 'info');
                });
                
            } catch (error) {
                log('operators-output', `❌ Search operators test failed: ${error.message}`, 'error');
            }
        }

        async function testFullAIAgent() {
            const output = document.getElementById('agent-output');
            const queryInput = document.getElementById('test-query');
            output.innerHTML = '';
            
            const query = queryInput.value || "What is artificial intelligence?";
            log('agent-output', `Testing full AI Agent with query: "${query}"`, 'info');
            
            if (!window.AIAgentBrowser) {
                log('agent-output', '❌ AIAgentBrowser not available', 'error');
                return;
            }

            try {
                const aiAgent = new AIAgentBrowser(mockApiManager);
                
                log('agent-output', 'Starting AI Agent browse...', 'info');
                
                const result = await aiAgent.browse(query, (progress) => {
                    log('agent-output', `📊 ${progress.stage}: ${progress.message}`, 'info');
                });

                if (result.success) {
                    log('agent-output', '✅ AI Agent completed successfully!', 'success');
                    log('agent-output', `Query: ${result.query}`, 'info');
                    log('agent-output', `Intent: ${result.analysis?.intent}`, 'info');
                    log('agent-output', `Sources: ${result.sources?.length || 0}`, 'info');
                    log('agent-output', `Test Mode: ${result.testMode ? 'Yes' : 'No'}`, 'info');
                    log('agent-output', `Answer: ${result.answer?.answer?.substring(0, 200)}...`, 'info');
                } else {
                    log('agent-output', `❌ AI Agent failed: ${result.error}`, 'error');
                }
                
            } catch (error) {
                log('agent-output', `❌ Full AI Agent test failed: ${error.message}`, 'error');
                console.error('Full test error:', error);
            }
        }

        // Auto-run component loading test on page load
        window.addEventListener('load', () => {
            setTimeout(testComponentLoading, 500);
        });
    </script>
</body>
</html>
